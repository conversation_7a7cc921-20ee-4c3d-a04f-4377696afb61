#!/bin/bash

# 带配置验证的测试运行脚本
# 先运行配置验证测试，通过后再运行主要测试

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR"

echo "=========================================="
echo "运行 SporadicProjectServiceImplTest (带配置验证)"
echo "项目目录: $PROJECT_DIR"
echo "=========================================="

# 检查环境变量文件
ENV_FILE=".env.test.local"
if [ -f "$ENV_FILE" ]; then
    echo "✓ 找到环境变量文件: $ENV_FILE"
    echo "加载环境变量..."
    source "$ENV_FILE"
    
    echo ""
    echo "当前测试配置:"
    echo "  组织ID: $TEST_ORGANIZATION_ID"
    echo "  服务标识: $TEST_SCID"
    echo "  认证令牌: ${Authorization:0:50}..."
else
    echo "❌ 未找到环境变量文件: $ENV_FILE"
    echo "使用默认配置运行测试"
    TEST_ORGANIZATION_ID="test-org-id"
    TEST_SCID="test-scid"
    Authorization="Bearer Authorization"
fi

echo ""
echo "=========================================="
echo "第一步: 运行配置验证测试"
echo "=========================================="

# 运行配置验证测试
echo "验证测试配置是否正确..."
mvn test -Dtest=ConfigurationValidationTest \
  -DTEST_ORGANIZATION_ID="$TEST_ORGANIZATION_ID" \
  -DTEST_SCID="$TEST_SCID" \
  -DAuthorization="$Authorization" \
  -Dspring.profiles.active=test

# 检查配置验证结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 配置验证测试通过！"
    echo ""
else
    echo ""
    echo "❌ 配置验证测试失败！"
    echo "请检查配置后重试"
    exit 1
fi

echo "=========================================="
echo "第二步: 运行主要测试"
echo "=========================================="

# 运行主要测试
echo "开始运行 SporadicProjectServiceImplTest..."
mvn test -Dtest=SporadicProjectServiceImplTest \
  -DTEST_ORGANIZATION_ID="$TEST_ORGANIZATION_ID" \
  -DTEST_SCID="$TEST_SCID" \
  -DAuthorization="$Authorization" \
  -Dspring.profiles.active=test

# 检查主要测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 所有测试通过！"
    echo ""
    echo "=========================================="
    echo "测试总结"
    echo "=========================================="
    echo "✓ 配置验证测试: 通过"
    echo "✓ 主要功能测试: 通过"
    echo "✓ 环境变量配置: 正确"
    echo "=========================================="
else
    echo ""
    echo "❌ 主要测试失败！"
    echo "请检查测试代码或配置"
    exit 1
fi
