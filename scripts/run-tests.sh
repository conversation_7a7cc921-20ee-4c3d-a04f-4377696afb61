#!/bin/bash

# 统一的测试运行脚本
# 支持多种运行模式：简化版（默认）、详细验证版、仅主要测试
#
# 用法:
#   ./scripts/run-tests.sh                    # 简化版（默认，推荐）
#   ./scripts/run-tests.sh --validation       # 详细验证版（两阶段）
#   ./scripts/run-tests.sh --simple           # 仅主要测试
#   ./scripts/run-tests.sh --help             # 显示帮助

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR"

# 默认运行模式
MODE="default"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --validation)
            MODE="validation"
            shift
            ;;
        --simple)
            MODE="simple"
            shift
            ;;
        --help|-h)
            echo "SporadicProjectServiceImplTest 测试运行脚本"
            echo ""
            echo "用法:"
            echo "  $0                    # 简化版（默认，推荐）"
            echo "  $0 --validation       # 详细验证版（两阶段测试）"
            echo "  $0 --simple           # 仅主要测试"
            echo "  $0 --help             # 显示此帮助信息"
            echo ""
            echo "模式说明:"
            echo "  简化版（默认）: 直接运行主要测试，setUp 中自动验证环境变量"
            echo "  详细验证版:     先运行配置验证测试，再运行主要测试"
            echo "  仅主要测试:     只运行主要功能测试，不包含额外验证"
            echo ""
            echo "环境变量配置:"
            echo "  创建 .env.test.local 文件设置测试参数"
            echo "  必填: TEST_SCID, Authorization"
            echo "  可选: TEST_ORGANIZATION_ID"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 显示运行模式
case $MODE in
    "validation")
        echo "=========================================="
        echo "运行模式: 详细验证版（两阶段测试）"
        echo "项目目录: $PROJECT_DIR"
        echo "=========================================="
        ;;
    "simple")
        echo "=========================================="
        echo "运行模式: 仅主要测试"
        echo "项目目录: $PROJECT_DIR"
        echo "=========================================="
        ;;
    *)
        echo "=========================================="
        echo "运行模式: 简化版（默认，推荐）"
        echo "项目目录: $PROJECT_DIR"
        echo "=========================================="
        ;;
esac

# 检查环境变量文件
ENV_FILE=".env.test.local"
if [ -f "$ENV_FILE" ]; then
    echo "✓ 找到环境变量文件: $ENV_FILE"
    echo "加载环境变量..."
    source "$ENV_FILE"
    
    echo ""
    echo "当前测试配置:"
    echo "  组织ID: ${TEST_ORGANIZATION_ID:-未设置}"
    echo "  服务标识: ${TEST_SCID:-未设置}"
    echo "  认证令牌: ${Authorization:0:50}..."
else
    echo "❌ 未找到环境变量文件: $ENV_FILE"
    echo "使用默认配置运行测试"
    TEST_ORGANIZATION_ID="test-org-id"
    TEST_SCID="test-scid"
    Authorization="Bearer Authorization"
fi

echo ""

# 根据模式执行不同的测试流程
case $MODE in
    "validation")
        echo "=========================================="
        echo "第一步: 运行配置验证测试"
        echo "=========================================="
        
        echo "验证测试配置是否正确..."
        mvn test -Dtest=ConfigurationValidationTest \
          -DTEST_ORGANIZATION_ID="$TEST_ORGANIZATION_ID" \
          -DTEST_SCID="$TEST_SCID" \
          -DAuthorization="$Authorization" \
          -Dspring.profiles.active=test
        
        if [ $? -eq 0 ]; then
            echo ""
            echo "✅ 配置验证测试通过！"
            echo ""
        else
            echo ""
            echo "❌ 配置验证测试失败！"
            echo "请检查配置后重试"
            exit 1
        fi
        
        echo "=========================================="
        echo "第二步: 运行主要测试"
        echo "=========================================="
        ;;
    "simple")
        echo "运行主要测试（不包含额外验证）..."
        ;;
    *)
        echo "运行测试（setUp 中自动验证环境变量）..."
        ;;
esac

# 运行主要测试
mvn test -Dtest=SporadicProjectServiceImplTest \
  -DTEST_ORGANIZATION_ID="$TEST_ORGANIZATION_ID" \
  -DTEST_SCID="$TEST_SCID" \
  -DAuthorization="$Authorization" \
  -Dspring.profiles.active=test

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 所有测试通过！"
    echo ""
    echo "=========================================="
    echo "测试总结"
    echo "=========================================="
    case $MODE in
        "validation")
            echo "✓ 配置验证测试: 通过"
            echo "✓ 主要功能测试: 通过"
            echo "✓ 环境变量配置: 正确"
            ;;
        "simple")
            echo "✓ 主要功能测试: 通过"
            echo "✓ 环境变量配置: 正确"
            ;;
        *)
            echo "✓ 环境变量验证: 在 setUp 中自动完成"
            echo "✓ 主要功能测试: 通过"
            echo "✓ 环境变量配置: 正确"
            ;;
    esac
    echo "=========================================="
else
    echo ""
    echo "❌ 测试失败！"
    echo "请检查测试代码或配置"
    exit 1
fi
