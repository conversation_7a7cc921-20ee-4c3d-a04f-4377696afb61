#!/bin/bash

# 测试运行脚本 - 使用环境变量自定义测试参数
# 用法: ./scripts/run-tests-with-env.sh [环境名称]

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 默认环境
ENVIRONMENT=${1:-"default"}

echo "=========================================="
echo "运行 SporadicProjectServiceImplTest"
echo "环境: $ENVIRONMENT"
echo "项目目录: $PROJECT_DIR"
echo "=========================================="

# 根据环境设置不同的参数
case $ENVIRONMENT in
    "dev")
        echo "使用开发环境配置..."
        export TEST_ORGANIZATION_ID="dev-org-001"
        export TEST_SCID="dev-service-scid"
        export TEST_AUTH_TOKEN="Bearer dev-token-12345"
        export TEST_PROJECT_NAME="开发环境测试工程"
        export TEST_PROJECT_AMOUNT="200000"
        export TEST_PROJECT_AREA="800"
        export TEST_ENABLE_PERMISSION="true"
        export TEST_ENABLE_GLOBAL_PARAMS="true"
        export TEST_ENABLE_EXCEPTIONS="true"
        ;;
    "staging")
        echo "使用预发布环境配置..."
        export TEST_ORGANIZATION_ID="staging-org-002"
        export TEST_SCID="staging-service-scid"
        export TEST_AUTH_TOKEN="Bearer staging-token-67890"
        export TEST_PROJECT_NAME="预发布环境测试工程"
        export TEST_PROJECT_AMOUNT="300000"
        export TEST_PROJECT_AREA="1000"
        export TEST_ENABLE_PERMISSION="true"
        export TEST_ENABLE_GLOBAL_PARAMS="true"
        export TEST_ENABLE_EXCEPTIONS="false"
        ;;
    "prod")
        echo "使用生产环境配置..."
        export TEST_ORGANIZATION_ID="prod-org-003"
        export TEST_SCID="prod-service-scid"
        export TEST_AUTH_TOKEN="Bearer prod-token-abcdef"
        export TEST_PROJECT_NAME="生产环境测试工程"
        export TEST_PROJECT_AMOUNT="500000"
        export TEST_PROJECT_AREA="1500"
        export TEST_ENABLE_PERMISSION="true"
        export TEST_ENABLE_GLOBAL_PARAMS="false"
        export TEST_ENABLE_EXCEPTIONS="false"
        ;;
    "minimal")
        echo "使用最小配置..."
        export TEST_ENABLE_PERMISSION="false"
        export TEST_ENABLE_GLOBAL_PARAMS="false"
        export TEST_ENABLE_EXCEPTIONS="false"
        ;;
    "custom")
        echo "使用自定义配置..."
        # 从环境变量文件加载（如果存在）
        if [ -f "$PROJECT_DIR/.env.test" ]; then
            echo "加载自定义环境变量文件: .env.test"
            source "$PROJECT_DIR/.env.test"
        else
            echo "未找到 .env.test 文件，使用默认配置"
        fi
        ;;
    *)
        echo "使用默认配置..."
        # 使用默认值，不设置环境变量
        ;;
esac

# 显示当前配置
echo ""
echo "当前测试配置:"
echo "  组织ID: ${TEST_ORGANIZATION_ID:-'默认值'}"
echo "  服务标识: ${TEST_SCID:-'默认值'}"
echo "  认证令牌: ${TEST_AUTH_TOKEN:-'默认值'}"
echo "  项目名称: ${TEST_PROJECT_NAME:-'默认值'}"
echo "  项目金额: ${TEST_PROJECT_AMOUNT:-'默认值'}"
echo "  项目面积: ${TEST_PROJECT_AREA:-'默认值'}"
echo "  权限测试: ${TEST_ENABLE_PERMISSION:-'默认值'}"
echo "  全局参数测试: ${TEST_ENABLE_GLOBAL_PARAMS:-'默认值'}"
echo "  异常测试: ${TEST_ENABLE_EXCEPTIONS:-'默认值'}"
echo ""

# 切换到项目目录
cd "$PROJECT_DIR"

# 运行测试
echo "开始运行测试..."
mvn test -Dtest=SporadicProjectServiceImplTest -Dspring.profiles.active=test

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "✅ 测试运行成功！"
    echo "=========================================="
else
    echo ""
    echo "=========================================="
    echo "❌ 测试运行失败！"
    echo "=========================================="
    exit 1
fi
