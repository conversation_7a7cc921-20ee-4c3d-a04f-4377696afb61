# 测试环境变量配置示例文件
# 复制此文件为 .env.test 并根据需要修改配置值

# ==================== 全局参数配置 ====================
# 组织机构ID
TEST_ORGANIZATION_ID=my-custom-org-id

# 系统/服务标识
TEST_SCID=my-custom-scid

# 认证令牌
TEST_AUTH_TOKEN=Bearer Authorization

# 用户ID
TEST_USER_ID=my-custom-user-id

# 用户名
TEST_USERNAME=mycustomuser

# 用户真实姓名
TEST_USER_REALNAME=我的自定义用户

# ==================== 项目数据配置 ====================
# 项目ID
TEST_PROJECT_ID=my-custom-project-id

# 项目名称
TEST_PROJECT_NAME=我的自定义测试工程

# 项目金额
TEST_PROJECT_AMOUNT=999999

# 项目面积
TEST_PROJECT_AREA=2000

# 项目地址
TEST_PROJECT_ADDRESS=我的自定义测试地址

# 项目编号
TEST_PROJECT_NUMBER=CUSTOM-2025-001

# ==================== 分类数据配置 ====================
# 分类父ID
TEST_CATE_PID=my-custom-cate-pid

# 分类ID
TEST_CATE_ID=my-custom-cate-id

# ==================== 区域数据配置 ====================
# 区域父ID
TEST_REGION_PID=my-custom-region-pid

# 区域ID
TEST_REGION_ID=my-custom-region-id

# 区域子ID
TEST_REGION_CID=my-custom-region-cid

# ==================== 建设单位数据配置 ====================
# 建设单位名称
TEST_CONSTRUCTOR_NAME=我的自定义建设单位

# 建设单位负责人
TEST_CONSTRUCTOR_CHARGER=我的自定义负责人

# 建设单位联系电话
TEST_OWNER_MOBILE=13900139000

# ==================== 施工单位数据配置 ====================
# 施工单位名称
TEST_CONTRACTOR_NAME=我的自定义施工单位

# 施工单位负责人
TEST_CONTRACTOR_CHARGER=我的自定义施工负责人

# 施工单位联系电话
TEST_CONTRACTOR_MOBILE=13900139001

# ==================== GIS点位数据配置 ====================
# GIS点位ID
TEST_POI_ID=my-custom-poi-id

# 纬度
TEST_POI_LAT=40.0000

# 经度
TEST_POI_LNG=116.0000

# ==================== 分页配置 ====================
# 默认页码
TEST_DEFAULT_PAGE=0

# 默认页大小
TEST_DEFAULT_SIZE=20

# ==================== 功能开关配置 ====================
# 是否启用权限验证测试
TEST_ENABLE_PERMISSION=true

# 是否启用全局参数验证测试
TEST_ENABLE_GLOBAL_PARAMS=true

# 是否启用异常场景测试
TEST_ENABLE_EXCEPTIONS=true
