package cn.shencom.server.service.impl;

import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.repos.SporadicProjectRepository;
import cn.shencom.config.TestConfiguration;
import cn.shencom.utils.UserUtil;
import cn.shencom.utils.UserUtil.UtilsRegion;
import cn.shencom.utils.UserUtil.UserUtilsProjectQueryDTO;
import cn.shencom.utils.XsgcContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.scloud.common.util.ScidContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * SporadicProjectServiceImpl 单元测试类
 * 专注于测试 getUserProjectList() 方法
 * 
 * 支持通过环境变量自定义测试参数：
 * - TEST_ORGANIZATION_ID: 组织机构ID
 * - TEST_SCID: 系统/服务标识
 * - Authorization: 认证令牌
 * 
 * 示例：
 * export TEST_ORGANIZATION_ID=my-org-id
 * export TEST_SCID=my-scid
 * export Authorization="Bearer my-token"
 * mvn test -Dtest=SporadicProjectServiceImplTest
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = TestConfiguration.class)
class SporadicProjectServiceImplTest {

    @InjectMocks
    private SporadicProjectServiceImpl sporadicProjectService;

    // 测试配置注入
    @Autowired
    private TestConfiguration testConfig;

    // Repository Mocks - 只保留必要的
    @Mock
    private SporadicProjectRepository sporadicProjectRepository;

    // Utility Mocks
    @Mock
    private UserUtil userUtil;

    // 测试数据
    private List<SporadicProjectRespDTO> testProjectList;

    // 全局参数测试数据（从配置中读取）
    private SecurityUser testUser;
    private String testOrganizationId;
    private String testScid;

    @BeforeEach
    void setUp() {
        // 验证必填环境变量
        TestConfiguration.validateRequiredEnvironmentVariables();
        // 从配置中初始化全局参数
        initGlobalParametersFromConfig();
        // 初始化测试数据
        initTestData();
        // 设置全局参数
        setupGlobalParameters();
    }

    @AfterEach
    void tearDown() {
        // 清理全局参数
        clearGlobalParameters();
    }

    /**
     * 从配置中初始化全局参数
     */
    private void initGlobalParametersFromConfig() {
        if (testConfig != null) {
            testOrganizationId = testConfig.getGlobalParams().getOrganizationId();
            testScid = testConfig.getGlobalParams().getScid();
            testConfig.getGlobalParams().getAuthToken();
        }
    }

    /**
     * 设置全局参数
     */
    private void setupGlobalParameters() {
        // 创建测试用户（使用默认值）
        testUser = mock(SecurityUser.class);
        String userId = "test-user-id";
        String username = "testuser";
        String userRealname = "测试用户";

        lenient().when(testUser.getId()).thenReturn(userId);
        lenient().when(testUser.getUid()).thenReturn(userId);
        lenient().when(testUser.getUsername()).thenReturn(username);
        lenient().when(testUser.getRealname()).thenReturn(userRealname);

        // 设置上下文
        XsgcContext.setOrganizationId(testOrganizationId);
        ScidContext.setScid(testScid);
    }

    /**
     * 清理全局参数
     */
    private void clearGlobalParameters() {
        XsgcContext.clearOrganizationId();
        ScidContext.clearScid();
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        // 创建测试项目列表
        testProjectList = createTestProjectList();
    }

    /**
     * 创建测试项目列表
     */
    private List<SporadicProjectRespDTO> createTestProjectList() {
        List<SporadicProjectRespDTO> list = new ArrayList<>();

        SporadicProjectRespDTO project1 = new SporadicProjectRespDTO();
        project1.setId("test-project-1");
        project1.setName("测试工程1");
        project1.setAmount(new BigDecimal("100000"));
        project1.setArea(new BigDecimal("500"));
        project1.setStatus(0);
        list.add(project1);

        SporadicProjectRespDTO project2 = new SporadicProjectRespDTO();
        project2.setId("test-project-2");
        project2.setName("测试工程2");
        project2.setAmount(new BigDecimal("200000"));
        project2.setArea(new BigDecimal("800"));
        project2.setStatus(1);
        list.add(project2);

        return list;
    }

    // ==================== getUserProjectList 测试方法 ====================

    @Test
    void testGetUserProjectList_WithRegionAndDto_Success() {
        // Given
        UtilsRegion region = mock(UtilsRegion.class);
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(testProjectList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(region, null);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("test-project-1", result.get(0).getId());
        assertEquals("测试工程1", result.get(0).getName());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_WithRegionAndDto_NoUserProject() {
        // Given
        UtilsRegion region = mock(UtilsRegion.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.empty());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(region, null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(sporadicProjectRepository, never()).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_NoParams_Success() {
        // Given
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(testProjectList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("test-project-1", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_EmptyResult() {
        // Given
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_NoUserProject() {
        // Given
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.empty());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(sporadicProjectRepository, never()).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

}
