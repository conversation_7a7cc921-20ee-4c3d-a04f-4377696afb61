package cn.shencom.server.service.impl;

import cn.shencom.model.*;
import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.server.service.*;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.config.TestConfiguration;
import cn.shencom.utils.UserUtil;
import cn.shencom.utils.UserUtil.UtilsRegion;
import cn.shencom.utils.UserUtil.UserUtilsProjectQueryDTO;
import cn.shencom.utils.XsgcContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.scloud.common.util.ScidContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SporadicProjectServiceImpl 单元测试类
 *
 * 支持通过环境变量自定义测试参数：
 * - TEST_ORGANIZATION_ID: 组织机构ID
 * - TEST_SCID: 系统/服务标识
 * - Authorization: 认证令牌
 * - TEST_USER_ID: 用户ID
 * - TEST_USERNAME: 用户名
 * - TEST_USER_REALNAME: 用户真实姓名
 *
 * 示例：
 * export TEST_ORGANIZATION_ID=my-org-id
 * export TEST_SCID=my-scid
 * mvn test -Dtest=SporadicProjectServiceImplTest
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest(classes = TestConfiguration.class)
class SporadicProjectServiceImplTest {

    @InjectMocks
    private SporadicProjectServiceImpl sporadicProjectService;

    // 测试配置注入
    @Autowired
    private TestConfiguration testConfig;

    // Repository Mocks
    @Mock
    private SporadicProjectRepository sporadicProjectRepository;
    
    @Mock
    private SporadicProjectCategoryRepository sporadicProjectCategoryRepository;
    
    @Mock
    private ComRegionRepository comRegionRepository;
    
    @Mock
    private GisPoiRepository gisPoiRepository;
    
    @Mock
    private MonitorOrderRepository monitorOrderRepository;
    
    @Mock
    private FnRmsv3MembersTypeRelateRepository fnRmsv3MembersTypeRelateRepository;

    // Service Mocks
    @Mock
    private IEngineeringMembersService engineeringMembersService;
    
    @Mock
    private IMonitorOrderService monitorOrderService;
    
    @Mock
    private ISporadicProjectMemoService sporadicProjectMemoService;

    // Utility Mocks
    @Mock
    private Validator validator;
    
    @Mock
    private UserUtil userUtil;

    // 测试数据
    private SporadicProject testProject;
    private SporadicProjectCreateDTO createDTO;
    private SporadicProjectUpdateDTO updateDTO;
    private SporadicProjectQueryDTO queryDTO;
    private SporadicProjectMobileQueryDTO mobileQueryDTO;
    private SporadicProjectRespDTO respDTO;
    private GisPoi testGisPoi;

    // 全局参数测试数据（从配置中读取）
    private SecurityUser testUser;
    private String testOrganizationId;
    private String testScid;
    private String testAuthToken;

    @BeforeEach
    void setUp() {
        // 从配置中初始化全局参数
        initGlobalParametersFromConfig();
        // 初始化测试数据
        initTestData();
        // 设置全局参数
        setupGlobalParameters();
    }

    @AfterEach
    void tearDown() {
        // 清理全局参数
        clearGlobalParameters();
    }

    /**
     * 从配置中初始化全局参数
     */
    private void initGlobalParametersFromConfig() {
        if (testConfig != null) {
            testOrganizationId = testConfig.getGlobalParams().getOrganizationId();
            testScid = testConfig.getGlobalParams().getScid();
            testAuthToken = testConfig.getGlobalParams().getAuthToken();
        } else {
            // 提供默认值作为后备
            testOrganizationId = "test-org-id";
            testScid = "test-scid";
            testAuthToken = "Bearer Authorization";
        }
    }

    /**
     * 设置全局参数
     */
    private void setupGlobalParameters() {
        // 创建测试用户（使用默认值）
        testUser = mock(SecurityUser.class);
        String userId = "test-user-id";
        String username = "testuser";
        String userRealname = "测试用户";

        when(testUser.getId()).thenReturn(userId);
        when(testUser.getUid()).thenReturn(userId);
        when(testUser.getUsername()).thenReturn(username);
        when(testUser.getRealname()).thenReturn(userRealname);

        // 设置上下文
        XsgcContext.setOrganizationId(testOrganizationId);
        ScidContext.setScid(testScid);

        // 注意：由于 ScContext 是静态方法，在实际测试中需要使用 PowerMock 或其他工具
        // 这里我们假设在实际的服务实现中会正确处理用户上下文
    }

    /**
     * 清理全局参数
     */
    private void clearGlobalParameters() {
        XsgcContext.clearOrganizationId();
        ScidContext.clearScid();
    }

    /**
     * 为测试设置全局参数
     */
    private void setupGlobalParametersForTest() {
        XsgcContext.setOrganizationId(testOrganizationId);
        ScidContext.setScid(testScid);
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        // 使用默认的测试数据
        testProject = SporadicProject.builder()
                .id("test-project-id")
                .name("测试工程")
                .organizationId(testOrganizationId)
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L)) // 明天
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .status(0)
                .monitorFlag(0)
                .poiId("test-poi-id")
                .lat(new BigDecimal("39.9042"))
                .lng(new BigDecimal("116.4074"))
                .createUser("test-user-id")
                .isDeleted(0)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();

        // 创建测试GIS点位
        testGisPoi = new GisPoi();
        testGisPoi.setId("test-poi-id");
        testGisPoi.setLat("39.9042");
        testGisPoi.setLng("116.4074");

        // 创建测试创建DTO
        createDTO = SporadicProjectCreateDTO.builder()
                .name("测试工程")
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L))
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .poiId("test-poi-id")
                .build();

        // 创建测试更新DTO
        updateDTO = SporadicProjectUpdateDTO.builder()
                .id("test-project-id")
                .name("更新后的测试工程")
                .amount(new BigDecimal("150000"))
                .area(new BigDecimal("600"))
                .poiId("test-poi-id")
                .build();

        // 创建测试查询DTO
        queryDTO = new SporadicProjectQueryDTO();
        queryDTO.setPage(0);
        queryDTO.setSize(10);

        // 创建测试移动端查询DTO
        mobileQueryDTO = new SporadicProjectMobileQueryDTO();
        mobileQueryDTO.setPage(0);
        mobileQueryDTO.setSize(10);

        // 创建测试响应DTO
        respDTO = new SporadicProjectRespDTO();
        respDTO.setId("test-project-id");
        respDTO.setName("测试工程");
        respDTO.setAmount(new BigDecimal("100000"));
        respDTO.setArea(new BigDecimal("500"));
        respDTO.setStatus(0);
    }

    /**
     * 创建测试用的分页结果
     */
    private Page<SporadicProjectRespDTO> createTestPage() {
        List<SporadicProjectRespDTO> content = Arrays.asList(respDTO);
        return new PageImpl<>(content, PageRequest.of(0, 10), 1);
    }

    /**
     * 创建测试用的项目列表
     */
    private List<SporadicProjectRespDTO> createTestList() {
        return Arrays.asList(respDTO);
    }

    // ==================== 查询相关方法测试 ====================

    @Test
    void testQuery_Success() {
        // Given
        setupGlobalParametersForTest();
        Page<SporadicProject> entityPage = new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(entityPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.query(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));

        // Verify global parameters are maintained
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        assertEquals(testScid, ScidContext.getScid());
    }

    @Test
    void testQuery_EmptyResult() {
        // Given
        Page<SporadicProject> emptyPage = new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(emptyPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.query(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testQueryByIds_Success() {
        // Given
        String projectId = "test-project-id";
        queryDTO.setIds(Arrays.asList(projectId));
        Page<SporadicProject> entityPage = new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(entityPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.queryByIds(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void testQueryByIds_EmptyIds() {
        // Given
        queryDTO.setIds(Collections.emptyList());

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.queryByIds(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testShow_Success() {
        // Given
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId("test-project-id");
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));

        // When
        SporadicProjectRespDTO result = sporadicProjectService.show(showDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        verify(sporadicProjectRepository).findById("test-project-id");
    }

    @Test
    void testShow_NotFound() {
        // Given
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId("non-existent-id");
        when(sporadicProjectRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // When
        SporadicProjectRespDTO result = sporadicProjectService.show(showDTO);

        // Then
        assertNull(result);
        verify(sporadicProjectRepository).findById("non-existent-id");
    }

    @Test
    void testMobileIndex_Success() {
        // Given
        Page<SporadicProjectRespDTO> expectedPage = createTestPage();
        when(sporadicProjectRepository.mobileIndex(any(SporadicProjectMobileQueryDTO.class), any(PageRequest.class)))
                .thenReturn(expectedPage);
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.mobileIndex(mobileQueryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("test-project-id", result.getContent().get(0).getId());
        verify(sporadicProjectRepository).mobileIndex(any(SporadicProjectMobileQueryDTO.class), any(PageRequest.class));
    }

    @Test
    void testMobileIndex_NoUserProject() {
        // Given
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.empty());

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.mobileIndex(mobileQueryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testMobileSelect_Success() {
        // Given
        List<SporadicProjectRespDTO> expectedList = createTestList();
        when(sporadicProjectRepository.mobileIndex(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.mobileSelect();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).mobileIndex(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testMobileSelect_EmptyResult() {
        // Given
        when(sporadicProjectRepository.mobileIndex(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.mobileSelect();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(sporadicProjectRepository).mobileIndex(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_WithRegionAndDto() {
        // Given
        UtilsRegion region = mock(UtilsRegion.class);
        List<SporadicProjectRespDTO> expectedList = createTestList();
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(region, queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_WithRegionAndDto_NoUserProject() {
        // Given
        UtilsRegion region = mock(UtilsRegion.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.empty());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(region, queryDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetUserProjectList_WithDto() {
        // Given
        List<SporadicProjectRespDTO> expectedList = createTestList();
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_NoParams() {
        // Given
        List<SporadicProjectRespDTO> expectedList = createTestList();
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    // ==================== CRUD操作测试 ====================

    @Test
    void testCreate_Success() {
        // Given
        setupGlobalParametersForTest();
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.create(createDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        assertEquals("测试工程", result.getName());
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
        // Note: createOrUpdateEngineer is a private method, so we verify the public methods it calls
        verify(engineeringMembersService, times(2)).createOrUpdate(any(EngineeringMembersUpdateDTO.class));
        verify(monitorOrderService).createOrder(any(SporadicProject.class));

        // Verify global parameters are maintained
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        assertEquals(testScid, ScidContext.getScid());
    }

    @Test
    void testCreate_PoiNotFound() {
        // Given
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.create(createDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testUpdate_Success() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.update(updateDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        verify(sporadicProjectRepository).findById("test-project-id");
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
        // Note: createOrUpdateEngineer is a private method, so we verify the public methods it calls
        verify(engineeringMembersService, times(2)).createOrUpdate(any(EngineeringMembersUpdateDTO.class));
    }

    @Test
    void testUpdate_ProjectNotFound() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.update(updateDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testUpdate_PoiNotFound() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.update(updateDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testUpdateStatus_Success() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        sporadicProjectService.updateStatus(updateDTO);

        // Then
        verify(sporadicProjectRepository).findById("test-project-id");
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
    }

    @Test
    void testUpdateStatus_ProjectNotFound() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.updateStatus(updateDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testDelete_Success() {
        // Given
        List<String> ids = Arrays.asList("test-project-id");
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        sporadicProjectService.delete(ids);

        // Then
        verify(sporadicProjectRepository).findById("test-project-id");
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
        verify(engineeringMembersService).removeMemberRelateByProjectId("test-project-id");
        verify(monitorOrderRepository).deleteByProjectId("test-project-id");
    }

    @Test
    void testDelete_EmptyIds() {
        // Given
        List<String> emptyIds = Collections.emptyList();

        // When
        sporadicProjectService.delete(emptyIds);

        // Then
        verify(sporadicProjectRepository, never()).findById(anyString());
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testDelete_ProjectNotFound() {
        // Given
        List<String> ids = Arrays.asList("non-existent-id");
        when(sporadicProjectRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // When
        sporadicProjectService.delete(ids);

        // Then
        verify(sporadicProjectRepository).findById("non-existent-id");
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
        verify(engineeringMembersService, never()).removeMemberRelateByProjectId(anyString());
        verify(monitorOrderRepository, never()).deleteByProjectId(anyString());
    }

    // ==================== 业务逻辑方法测试 ====================

    @Test
    void testUpdateMonitorFlag_Success() {
        // Given
        String projectId = "test-project-id";
        when(sporadicProjectRepository.updateMonitorFlagById(projectId)).thenReturn(1);

        // When
        sporadicProjectService.updateMonitorFlag(projectId);

        // Then
        verify(sporadicProjectRepository).updateMonitorFlagById(projectId);
    }

    @Test
    void testAutoUpdateStatus_Success() {
        // Given
        Date today = new Date();
        Date yesterday = new Date(System.currentTimeMillis() - 86400000L);

        List<SporadicProject> needToStart = Arrays.asList(testProject);
        List<SporadicProject> needToEnd = Arrays.asList(testProject);

        when(sporadicProjectRepository.findByStartAtLessThanAndStatus(any(Date.class), eq(0)))
                .thenReturn(needToStart);
        when(sporadicProjectRepository.findByEndAtLessThanAndStatus(any(Date.class), eq(1)))
                .thenReturn(needToEnd);
        when(sporadicProjectRepository.saveAll(anyList())).thenReturn(needToStart);

        // When
        sporadicProjectService.autoUpdateStatus();

        // Then
        verify(sporadicProjectRepository).findByStartAtLessThanAndStatus(any(Date.class), eq(0));
        verify(sporadicProjectRepository).findByEndAtLessThanAndStatus(any(Date.class), eq(1));
        verify(sporadicProjectRepository, times(2)).saveAll(anyList());
    }

    @Test
    void testAutoUpdateStatus_NoProjectsToUpdate() {
        // Given
        when(sporadicProjectRepository.findByStartAtLessThanAndStatus(any(Date.class), eq(0)))
                .thenReturn(Collections.emptyList());
        when(sporadicProjectRepository.findByEndAtLessThanAndStatus(any(Date.class), eq(1)))
                .thenReturn(Collections.emptyList());

        // When
        sporadicProjectService.autoUpdateStatus();

        // Then
        verify(sporadicProjectRepository).findByStartAtLessThanAndStatus(any(Date.class), eq(0));
        verify(sporadicProjectRepository).findByEndAtLessThanAndStatus(any(Date.class), eq(1));
        verify(sporadicProjectRepository, times(2)).saveAll(anyList());
    }

    @Test
    void testAutoUpdateMonitorFlag_Success() {
        // Given
        List<SporadicProject> projects = Arrays.asList(testProject);
        when(sporadicProjectRepository.findByOrganizationId(anyString())).thenReturn(projects);
        when(sporadicProjectRepository.saveAll(anyList())).thenReturn(projects);

        // When
        sporadicProjectService.autoUpdateMonitorFlag();

        // Then
        verify(sporadicProjectRepository).findByOrganizationId(anyString());
        verify(sporadicProjectRepository).saveAll(anyList());
    }

    // ==================== 导入导出功能测试 ====================

    @Test
    void testExport_Success() {
        // Given
        Page<SporadicProjectRespDTO> mockPage = createTestPage();
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1));

        // When
        sporadicProjectService.export(queryDTO);

        // Then
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));
        // Note: ScExport.export is a static method, so we can't easily verify it in unit tests
    }

    @Test
    void testImportExcel_Success() {
        // Given
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("test.xlsx");
        when(mockFile.isEmpty()).thenReturn(false);

        // Mock the Excel parsing and processing
        // Note: This is a complex method that involves file processing,
        // so we're testing the basic flow rather than the detailed implementation

        // When
        Result<?> result = sporadicProjectService.importExcel(mockFile);

        // Then
        assertNotNull(result);
        // The actual result depends on the file content and processing logic
    }

    @Test
    void testImportExcel_EmptyFile() {
        // Given
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.isEmpty()).thenReturn(true);

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.importExcel(mockFile));
    }

    @Test
    void testImportExcel_NullFile() {
        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.importExcel(null));
    }

    // ==================== 测试数据准备和工具方法 ====================

    /**
     * 创建测试用的SporadicProject实体
     */
    private SporadicProject createTestSporadicProject(String id, String name) {
        return SporadicProject.builder()
                .id(id)
                .name(name)
                .organizationId("test-org-id")
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L))
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .status(0)
                .monitorFlag(0)
                .poiId("test-poi-id")
                .lat(new BigDecimal("39.9042"))
                .lng(new BigDecimal("116.4074"))
                .createUser("test-user-id")
                .isDeleted(0)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
    }

    /**
     * 创建测试用的SporadicProjectCreateDTO
     */
    private SporadicProjectCreateDTO createTestCreateDTO(String name) {
        return SporadicProjectCreateDTO.builder()
                .name(name)
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L))
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .poiId("test-poi-id")
                .build();
    }

    /**
     * 创建测试用的SporadicProjectUpdateDTO
     */
    private SporadicProjectUpdateDTO createTestUpdateDTO(String id, String name) {
        return SporadicProjectUpdateDTO.builder()
                .id(id)
                .name(name)
                .amount(new BigDecimal("150000"))
                .area(new BigDecimal("600"))
                .poiId("test-poi-id")
                .build();
    }

    /**
     * 创建测试用的SporadicProjectRespDTO
     */
    private SporadicProjectRespDTO createTestRespDTO(String id, String name) {
        SporadicProjectRespDTO dto = new SporadicProjectRespDTO();
        dto.setId(id);
        dto.setName(name);
        dto.setAmount(new BigDecimal("100000"));
        dto.setArea(new BigDecimal("500"));
        dto.setStatus(0);
        return dto;
    }

    /**
     * 创建测试用的GisPoi
     */
    private GisPoi createTestGisPoi(String id) {
        GisPoi poi = new GisPoi();
        poi.setId(id);
        poi.setLat("39.9042");
        poi.setLng("116.4074");
        return poi;
    }

    /**
     * 设置通用的Mock行为
     */
    private void setupCommonMocks() {
        // 设置常用的Mock行为
        when(gisPoiRepository.findById(anyString())).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);
        when(sporadicProjectRepository.findById(anyString())).thenReturn(Optional.of(testProject));
    }

    /**
     * 验证常用的方法调用
     */
    private void verifyCommonCalls() {
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
    }

    // ==================== 边界条件和异常场景测试 ====================

    @Test
    void testCreate_WithNullDTO() {
        // When & Then
        assertThrows(NullPointerException.class, () -> sporadicProjectService.create(null));
    }

    @Test
    void testUpdate_WithNullDTO() {
        // When & Then
        assertThrows(NullPointerException.class, () -> sporadicProjectService.update(null));
    }

    @Test
    void testDelete_WithNullIds() {
        // When
        sporadicProjectService.delete(null);

        // Then
        verify(sporadicProjectRepository, never()).findById(anyString());
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testShow_WithNullDTO() {
        // When & Then
        assertThrows(NullPointerException.class, () -> sporadicProjectService.show(null));
    }

    // ==================== 全局参数和权限验证测试 ====================

    @Test
    void testGlobalParameters_OrganizationIdSet() {
        // Given
        String expectedOrgId = "test-org-id";
        XsgcContext.setOrganizationId(expectedOrgId);

        // When
        String actualOrgId = XsgcContext.getOrganizationId();

        // Then
        assertEquals(expectedOrgId, actualOrgId);

        // Cleanup
        XsgcContext.clearOrganizationId();
    }

    @Test
    void testGlobalParameters_ScidSet() {
        // Given
        String expectedScid = "test-scid";
        ScidContext.setScid(expectedScid);

        // When
        String actualScid = ScidContext.getScid();

        // Then
        assertEquals(expectedScid, actualScid);

        // Cleanup
        ScidContext.clearScid();
    }

    @Test
    void testQuery_WithOrganizationContext() {
        // Given
        XsgcContext.setOrganizationId(testOrganizationId);
        Page<SporadicProject> entityPage = new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(entityPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.query(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));

        // Cleanup
        XsgcContext.clearOrganizationId();
    }

    @Test
    void testCreate_WithOrganizationContext() {
        // Given
        XsgcContext.setOrganizationId(testOrganizationId);
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.create(createDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        verify(sporadicProjectRepository).save(any(SporadicProject.class));

        // Cleanup
        XsgcContext.clearOrganizationId();
    }

    @Test
    void testUpdate_WithOrganizationContext() {
        // Given
        XsgcContext.setOrganizationId(testOrganizationId);
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.update(updateDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        verify(sporadicProjectRepository).save(any(SporadicProject.class));

        // Cleanup
        XsgcContext.clearOrganizationId();
    }

    @Test
    void testQuery_WithoutOrganizationContext() {
        // Given
        XsgcContext.clearOrganizationId(); // 确保没有组织上下文
        Page<SporadicProject> entityPage = new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(entityPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.query(queryDTO);

        // Then
        assertNotNull(result);
        assertNull(XsgcContext.getOrganizationId());
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void testCreate_WithInvalidOrganizationContext() {
        // Given
        XsgcContext.setOrganizationId(""); // 设置空的组织ID
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));

        // When & Then
        // 根据实际业务逻辑，空的组织ID可能会导致异常
        // 这里假设服务会验证组织ID的有效性
        try {
            sporadicProjectService.create(createDTO);
            // 如果没有抛出异常，验证组织ID是否被正确处理
            assertEquals("", XsgcContext.getOrganizationId());
        } catch (ScException e) {
            // 如果抛出异常，验证异常信息
            assertTrue(e.getMessage().contains("组织") || e.getMessage().contains("权限"));
        } finally {
            XsgcContext.clearOrganizationId();
        }
    }

    @Test
    void testScidContext_SetAndGet() {
        // Given
        String testScidValue = "test-scid-value";

        // When
        ScidContext.setScid(testScidValue);
        String retrievedScid = ScidContext.getScid();

        // Then
        assertEquals(testScidValue, retrievedScid);

        // Cleanup
        ScidContext.clearScid();
        assertNull(ScidContext.getScid());
    }

    @Test
    void testMultipleContextParameters() {
        // Given
        String orgId = "multi-test-org";
        String scidValue = "multi-test-scid";

        // When
        XsgcContext.setOrganizationId(orgId);
        ScidContext.setScid(scidValue);

        // Then
        assertEquals(orgId, XsgcContext.getOrganizationId());
        assertEquals(scidValue, ScidContext.getScid());

        // Cleanup
        XsgcContext.clearOrganizationId();
        ScidContext.clearScid();
        assertNull(XsgcContext.getOrganizationId());
        assertNull(ScidContext.getScid());
    }

    /**
     * 测试用户权限验证辅助方法
     */
    private void setupUserPermissions(String userId, String organizationId) {
        XsgcContext.setOrganizationId(organizationId);
        // 在实际测试中，这里可以设置用户的具体权限
    }

    /**
     * 验证权限检查的辅助方法
     */
    private void verifyPermissionCheck(String expectedUserId, String expectedOrgId) {
        assertEquals(expectedOrgId, XsgcContext.getOrganizationId());
        // 在实际测试中，这里可以验证权限检查逻辑
    }

    // ==================== 权限验证专项测试 ====================

    @Test
    void testAuthorizationHeader_Simulation() {
        // Given
        String authToken = testConfig != null ? testConfig.getGlobalParams().getAuthToken() : "Bearer test-jwt-token";
        String orgId = testConfig != null ? testConfig.getGlobalParams().getOrganizationId() : "auth-test-org-id";

        // 模拟 Authorization header 处理
        setupGlobalParametersForTest();
        XsgcContext.setOrganizationId(orgId);

        // When
        String retrievedOrgId = XsgcContext.getOrganizationId();
        String retrievedScid = ScidContext.getScid();

        // Then
        assertEquals(orgId, retrievedOrgId);
        assertEquals(testScid, retrievedScid);

        // Cleanup
        XsgcContext.clearOrganizationId();
        ScidContext.clearScid();
    }

    @Test
    void testPermissionValidation_WithValidUser() {
        // Given
        setupGlobalParametersForTest();
        String poiId = "test-poi-id";
        when(gisPoiRepository.findById(poiId)).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.create(createDTO);

        // Then
        assertNotNull(result);
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
    }

    @Test
    void testPermissionValidation_WithInvalidOrganization() {
        // Given
        XsgcContext.setOrganizationId(null); // 模拟无效的组织ID
        String poiId = "test-poi-id";
        when(gisPoiRepository.findById(poiId)).thenReturn(Optional.of(testGisPoi));

        // When & Then
        try {
            sporadicProjectService.create(createDTO);
            // 如果没有抛出异常，验证是否正确处理了空组织ID
            assertNull(XsgcContext.getOrganizationId());
        } catch (Exception e) {
            // 如果抛出异常，验证异常类型和消息
            assertTrue(e instanceof ScException || e instanceof NullPointerException);
        } finally {
            XsgcContext.clearOrganizationId();
        }
    }

    @Test
    void testScidValidation_WithValidScid() {
        // Given
        String validScid = "valid-scid-123";
        ScidContext.setScid(validScid);

        // When
        String retrievedScid = ScidContext.getScid();

        // Then
        assertEquals(validScid, retrievedScid);

        // Cleanup
        ScidContext.clearScid();
    }

    @Test
    void testScidValidation_WithNullScid() {
        // Given
        ScidContext.clearScid(); // 确保 scid 为 null

        // When
        String retrievedScid = ScidContext.getScid();

        // Then
        assertNull(retrievedScid);
    }

    @Test
    void testMultipleOperations_WithSameContext() {
        // Given
        setupGlobalParametersForTest();
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));

        // When - 执行多个操作
        SporadicProject created = sporadicProjectService.create(createDTO);
        SporadicProject updated = sporadicProjectService.update(updateDTO);

        // Then - 验证上下文在多个操作中保持一致
        assertNotNull(created);
        assertNotNull(updated);
        assertEquals(testOrganizationId, XsgcContext.getOrganizationId());
        assertEquals(testScid, ScidContext.getScid());

        verify(sporadicProjectRepository, times(2)).save(any(SporadicProject.class));
    }

    @Test
    void testContextIsolation_BetweenTests() {
        // Given - 设置特定的上下文
        String isolatedOrgId = "isolated-org-id";
        String isolatedScid = "isolated-scid";

        XsgcContext.setOrganizationId(isolatedOrgId);
        ScidContext.setScid(isolatedScid);

        // When
        String retrievedOrgId = XsgcContext.getOrganizationId();
        String retrievedScid = ScidContext.getScid();

        // Then
        assertEquals(isolatedOrgId, retrievedOrgId);
        assertEquals(isolatedScid, retrievedScid);

        // Cleanup - 确保不影响其他测试
        XsgcContext.clearOrganizationId();
        ScidContext.clearScid();

        // Verify cleanup
        assertNull(XsgcContext.getOrganizationId());
        assertNull(ScidContext.getScid());
    }

    @Test
    void testHeaderParameterValidation() {
        // Given - 模拟 HTTP header 参数
        String scidHeader = testConfig != null ? testConfig.getGlobalParams().getScid() : "test-scid-header";
        String orgIdHeader = testConfig != null ? testConfig.getGlobalParams().getOrganizationId() : "test-org-header";

        // When - 设置参数（模拟从 header 中提取）
        ScidContext.setScid(scidHeader);
        XsgcContext.setOrganizationId(orgIdHeader);

        // Then - 验证参数正确设置
        assertEquals(scidHeader, ScidContext.getScid());
        assertEquals(orgIdHeader, XsgcContext.getOrganizationId());

        // Cleanup
        ScidContext.clearScid();
        XsgcContext.clearOrganizationId();
    }

    // ==================== 配置验证测试 ====================

    @Test
    void testConfigurationLoading() {
        // 验证配置是否正确加载
        if (testConfig != null) {
            // 验证全局参数配置
            assertNotNull(testConfig.getGlobalParams());
            assertNotNull(testConfig.getGlobalParams().getOrganizationId());
            assertNotNull(testConfig.getGlobalParams().getScid());
            assertNotNull(testConfig.getGlobalParams().getAuthToken());

            // 输出配置信息用于调试
            System.out.println("=== 测试配置信息 ===");
            System.out.println("组织ID: " + testConfig.getGlobalParams().getOrganizationId());
            System.out.println("服务标识: " + testConfig.getGlobalParams().getScid());
            System.out.println("认证令牌: " + testConfig.getGlobalParams().getAuthToken());
            System.out.println("==================");
        } else {
            System.out.println("警告: 测试配置未加载，使用默认值");
        }
    }

    @Test
    void testEnvironmentVariableOverride() {
        // 这个测试验证环境变量是否能正确覆盖配置文件中的值
        // 注意：这个测试需要在设置了环境变量的情况下运行才能验证覆盖效果

        if (testConfig != null) {
            String orgId = testConfig.getGlobalParams().getOrganizationId();
            String scid = testConfig.getGlobalParams().getScid();

            // 如果环境变量 TEST_ORGANIZATION_ID 被设置，配置值应该反映环境变量的值
            String envOrgId = System.getenv("TEST_ORGANIZATION_ID");
            if (envOrgId != null && !envOrgId.isEmpty()) {
                assertEquals(envOrgId, orgId, "组织ID应该使用环境变量的值");
                System.out.println("✓ 环境变量 TEST_ORGANIZATION_ID 正确覆盖了配置文件值: " + orgId);
            }

            // 如果环境变量 TEST_SCID 被设置，配置值应该反映环境变量的值
            String envScid = System.getenv("TEST_SCID");
            if (envScid != null && !envScid.isEmpty()) {
                assertEquals(envScid, scid, "SCID应该使用环境变量的值");
                System.out.println("✓ 环境变量 TEST_SCID 正确覆盖了配置文件值: " + scid);
            }

            // 如果没有设置环境变量，应该使用默认值
            if (envOrgId == null || envOrgId.isEmpty()) {
                System.out.println("ℹ 未设置环境变量 TEST_ORGANIZATION_ID，使用默认值: " + orgId);
            }
            if (envScid == null || envScid.isEmpty()) {
                System.out.println("ℹ 未设置环境变量 TEST_SCID，使用默认值: " + scid);
            }
        }
    }
}
