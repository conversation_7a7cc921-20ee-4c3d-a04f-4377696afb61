package cn.shencom.server.service.impl;

import cn.shencom.model.*;
import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.server.service.*;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.utils.UserUtil;
import cn.shencom.utils.UserUtil.UtilsRegion;
import cn.shencom.utils.UserUtil.UserUtilsProjectQueryDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SporadicProjectServiceImpl 单元测试类
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@ExtendWith(MockitoExtension.class)
class SporadicProjectServiceImplTest {

    @InjectMocks
    private SporadicProjectServiceImpl sporadicProjectService;

    // Repository Mocks
    @Mock
    private SporadicProjectRepository sporadicProjectRepository;
    
    @Mock
    private SporadicProjectCategoryRepository sporadicProjectCategoryRepository;
    
    @Mock
    private ComRegionRepository comRegionRepository;
    
    @Mock
    private GisPoiRepository gisPoiRepository;
    
    @Mock
    private MonitorOrderRepository monitorOrderRepository;
    
    @Mock
    private FnRmsv3MembersTypeRelateRepository fnRmsv3MembersTypeRelateRepository;

    // Service Mocks
    @Mock
    private IEngineeringMembersService engineeringMembersService;
    
    @Mock
    private IMonitorOrderService monitorOrderService;
    
    @Mock
    private ISporadicProjectMemoService sporadicProjectMemoService;

    // Utility Mocks
    @Mock
    private Validator validator;
    
    @Mock
    private UserUtil userUtil;

    // 测试数据
    private SporadicProject testProject;
    private SporadicProjectCreateDTO createDTO;
    private SporadicProjectUpdateDTO updateDTO;
    private SporadicProjectQueryDTO queryDTO;
    private SporadicProjectMobileQueryDTO mobileQueryDTO;
    private SporadicProjectRespDTO respDTO;
    private GisPoi testGisPoi;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        initTestData();
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        // 创建测试项目实体
        testProject = SporadicProject.builder()
                .id("test-project-id")
                .name("测试工程")
                .organizationId("test-org-id")
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L)) // 明天
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .status(0)
                .monitorFlag(0)
                .poiId("test-poi-id")
                .lat(new BigDecimal("39.9042"))
                .lng(new BigDecimal("116.4074"))
                .createUser("test-user-id")
                .isDeleted(0)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();

        // 创建测试GIS点位
        testGisPoi = new GisPoi();
        testGisPoi.setId("test-poi-id");
        testGisPoi.setLat("39.9042");
        testGisPoi.setLng("116.4074");

        // 创建测试创建DTO
        createDTO = SporadicProjectCreateDTO.builder()
                .name("测试工程")
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L))
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .poiId("test-poi-id")
                .build();

        // 创建测试更新DTO
        updateDTO = SporadicProjectUpdateDTO.builder()
                .id("test-project-id")
                .name("更新后的测试工程")
                .amount(new BigDecimal("150000"))
                .area(new BigDecimal("600"))
                .poiId("test-poi-id")
                .build();

        // 创建测试查询DTO
        queryDTO = new SporadicProjectQueryDTO();
        queryDTO.setPage(0);
        queryDTO.setSize(10);

        // 创建测试移动端查询DTO
        mobileQueryDTO = new SporadicProjectMobileQueryDTO();
        mobileQueryDTO.setPage(0);
        mobileQueryDTO.setSize(10);

        // 创建测试响应DTO
        respDTO = new SporadicProjectRespDTO();
        respDTO.setId("test-project-id");
        respDTO.setName("测试工程");
        respDTO.setAmount(new BigDecimal("100000"));
        respDTO.setArea(new BigDecimal("500"));
        respDTO.setStatus(0);
    }

    /**
     * 创建测试用的分页结果
     */
    private Page<SporadicProjectRespDTO> createTestPage() {
        List<SporadicProjectRespDTO> content = Arrays.asList(respDTO);
        return new PageImpl<>(content, PageRequest.of(0, 10), 1);
    }

    /**
     * 创建测试用的项目列表
     */
    private List<SporadicProjectRespDTO> createTestList() {
        return Arrays.asList(respDTO);
    }

    // ==================== 查询相关方法测试 ====================

    @Test
    void testQuery_Success() {
        // Given
        Page<SporadicProject> entityPage = new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(entityPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.query(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void testQuery_EmptyResult() {
        // Given
        Page<SporadicProject> emptyPage = new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(emptyPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.query(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testQueryByIds_Success() {
        // Given
        queryDTO.setIds(Arrays.asList("test-project-id"));
        Page<SporadicProject> entityPage = new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1);
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(entityPage);

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.queryByIds(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));
    }

    @Test
    void testQueryByIds_EmptyIds() {
        // Given
        queryDTO.setIds(Collections.emptyList());

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.queryByIds(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testShow_Success() {
        // Given
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId("test-project-id");
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));

        // When
        SporadicProjectRespDTO result = sporadicProjectService.show(showDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        verify(sporadicProjectRepository).findById("test-project-id");
    }

    @Test
    void testShow_NotFound() {
        // Given
        ScShowDTO showDTO = new ScShowDTO();
        showDTO.setId("non-existent-id");
        when(sporadicProjectRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // When
        SporadicProjectRespDTO result = sporadicProjectService.show(showDTO);

        // Then
        assertNull(result);
        verify(sporadicProjectRepository).findById("non-existent-id");
    }

    @Test
    void testMobileIndex_Success() {
        // Given
        Page<SporadicProjectRespDTO> expectedPage = createTestPage();
        when(sporadicProjectRepository.mobileIndex(any(SporadicProjectMobileQueryDTO.class), any(PageRequest.class)))
                .thenReturn(expectedPage);
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.mobileIndex(mobileQueryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("test-project-id", result.getContent().get(0).getId());
        verify(sporadicProjectRepository).mobileIndex(any(SporadicProjectMobileQueryDTO.class), any(PageRequest.class));
    }

    @Test
    void testMobileIndex_NoUserProject() {
        // Given
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.empty());

        // When
        Page<SporadicProjectRespDTO> result = sporadicProjectService.mobileIndex(mobileQueryDTO);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testMobileSelect_Success() {
        // Given
        List<SporadicProjectRespDTO> expectedList = createTestList();
        when(sporadicProjectRepository.mobileIndex(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.mobileSelect();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).mobileIndex(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testMobileSelect_EmptyResult() {
        // Given
        when(sporadicProjectRepository.mobileIndex(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.mobileSelect();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(sporadicProjectRepository).mobileIndex(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_WithRegionAndDto() {
        // Given
        UtilsRegion region = mock(UtilsRegion.class);
        List<SporadicProjectRespDTO> expectedList = createTestList();
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(region, queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_WithRegionAndDto_NoUserProject() {
        // Given
        UtilsRegion region = mock(UtilsRegion.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.empty());

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(region, queryDTO);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetUserProjectList_WithDto() {
        // Given
        List<SporadicProjectRespDTO> expectedList = createTestList();
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList(queryDTO);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    @Test
    void testGetUserProjectList_NoParams() {
        // Given
        List<SporadicProjectRespDTO> expectedList = createTestList();
        UserUtilsProjectQueryDTO mockQueryDTO = mock(UserUtilsProjectQueryDTO.class);
        when(userUtil.createUserProjectDto(any(UtilsRegion.class))).thenReturn(Optional.of(mockQueryDTO));
        when(sporadicProjectRepository.queryProjects(any(SporadicProjectMobileQueryDTO.class)))
                .thenReturn(expectedList);

        // When
        List<SporadicProjectRespDTO> result = sporadicProjectService.getUserProjectList();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-project-id", result.get(0).getId());
        verify(sporadicProjectRepository).queryProjects(any(SporadicProjectMobileQueryDTO.class));
    }

    // ==================== CRUD操作测试 ====================

    @Test
    void testCreate_Success() {
        // Given
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.create(createDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        assertEquals("测试工程", result.getName());
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
        // Note: createOrUpdateEngineer is a private method, so we verify the public methods it calls
        verify(engineeringMembersService, times(2)).createOrUpdate(any(EngineeringMembersUpdateDTO.class));
        verify(monitorOrderService).createOrder(any(SporadicProject.class));
    }

    @Test
    void testCreate_PoiNotFound() {
        // Given
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.create(createDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testUpdate_Success() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        SporadicProject result = sporadicProjectService.update(updateDTO);

        // Then
        assertNotNull(result);
        assertEquals("test-project-id", result.getId());
        verify(sporadicProjectRepository).findById("test-project-id");
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
        // Note: createOrUpdateEngineer is a private method, so we verify the public methods it calls
        verify(engineeringMembersService, times(2)).createOrUpdate(any(EngineeringMembersUpdateDTO.class));
    }

    @Test
    void testUpdate_ProjectNotFound() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.update(updateDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testUpdate_PoiNotFound() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(gisPoiRepository.findById("test-poi-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.update(updateDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testUpdateStatus_Success() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        sporadicProjectService.updateStatus(updateDTO);

        // Then
        verify(sporadicProjectRepository).findById("test-project-id");
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
    }

    @Test
    void testUpdateStatus_ProjectNotFound() {
        // Given
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.updateStatus(updateDTO));
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testDelete_Success() {
        // Given
        List<String> ids = Arrays.asList("test-project-id");
        when(sporadicProjectRepository.findById("test-project-id")).thenReturn(Optional.of(testProject));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);

        // When
        sporadicProjectService.delete(ids);

        // Then
        verify(sporadicProjectRepository).findById("test-project-id");
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
        verify(engineeringMembersService).removeMemberRelateByProjectId("test-project-id");
        verify(monitorOrderRepository).deleteByProjectId("test-project-id");
    }

    @Test
    void testDelete_EmptyIds() {
        // Given
        List<String> emptyIds = Collections.emptyList();

        // When
        sporadicProjectService.delete(emptyIds);

        // Then
        verify(sporadicProjectRepository, never()).findById(anyString());
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testDelete_ProjectNotFound() {
        // Given
        List<String> ids = Arrays.asList("non-existent-id");
        when(sporadicProjectRepository.findById("non-existent-id")).thenReturn(Optional.empty());

        // When
        sporadicProjectService.delete(ids);

        // Then
        verify(sporadicProjectRepository).findById("non-existent-id");
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
        verify(engineeringMembersService, never()).removeMemberRelateByProjectId(anyString());
        verify(monitorOrderRepository, never()).deleteByProjectId(anyString());
    }

    // ==================== 业务逻辑方法测试 ====================

    @Test
    void testUpdateMonitorFlag_Success() {
        // Given
        String projectId = "test-project-id";
        when(sporadicProjectRepository.updateMonitorFlagById(projectId)).thenReturn(1);

        // When
        sporadicProjectService.updateMonitorFlag(projectId);

        // Then
        verify(sporadicProjectRepository).updateMonitorFlagById(projectId);
    }

    @Test
    void testAutoUpdateStatus_Success() {
        // Given
        Date today = new Date();
        Date yesterday = new Date(System.currentTimeMillis() - 86400000L);

        List<SporadicProject> needToStart = Arrays.asList(testProject);
        List<SporadicProject> needToEnd = Arrays.asList(testProject);

        when(sporadicProjectRepository.findByStartAtLessThanAndStatus(any(Date.class), eq(0)))
                .thenReturn(needToStart);
        when(sporadicProjectRepository.findByEndAtLessThanAndStatus(any(Date.class), eq(1)))
                .thenReturn(needToEnd);
        when(sporadicProjectRepository.saveAll(anyList())).thenReturn(needToStart);

        // When
        sporadicProjectService.autoUpdateStatus();

        // Then
        verify(sporadicProjectRepository).findByStartAtLessThanAndStatus(any(Date.class), eq(0));
        verify(sporadicProjectRepository).findByEndAtLessThanAndStatus(any(Date.class), eq(1));
        verify(sporadicProjectRepository, times(2)).saveAll(anyList());
    }

    @Test
    void testAutoUpdateStatus_NoProjectsToUpdate() {
        // Given
        when(sporadicProjectRepository.findByStartAtLessThanAndStatus(any(Date.class), eq(0)))
                .thenReturn(Collections.emptyList());
        when(sporadicProjectRepository.findByEndAtLessThanAndStatus(any(Date.class), eq(1)))
                .thenReturn(Collections.emptyList());

        // When
        sporadicProjectService.autoUpdateStatus();

        // Then
        verify(sporadicProjectRepository).findByStartAtLessThanAndStatus(any(Date.class), eq(0));
        verify(sporadicProjectRepository).findByEndAtLessThanAndStatus(any(Date.class), eq(1));
        verify(sporadicProjectRepository, times(2)).saveAll(anyList());
    }

    @Test
    void testAutoUpdateMonitorFlag_Success() {
        // Given
        List<SporadicProject> projects = Arrays.asList(testProject);
        when(sporadicProjectRepository.findByOrganizationId(anyString())).thenReturn(projects);
        when(sporadicProjectRepository.saveAll(anyList())).thenReturn(projects);

        // When
        sporadicProjectService.autoUpdateMonitorFlag();

        // Then
        verify(sporadicProjectRepository).findByOrganizationId(anyString());
        verify(sporadicProjectRepository).saveAll(anyList());
    }

    // ==================== 导入导出功能测试 ====================

    @Test
    void testExport_Success() {
        // Given
        Page<SporadicProjectRespDTO> mockPage = createTestPage();
        when(sporadicProjectRepository.findAll(any(Specification.class), any(PageRequest.class)))
                .thenReturn(new PageImpl<>(Arrays.asList(testProject), PageRequest.of(0, 10), 1));

        // When
        sporadicProjectService.export(queryDTO);

        // Then
        verify(sporadicProjectRepository).findAll(any(Specification.class), any(PageRequest.class));
        // Note: ScExport.export is a static method, so we can't easily verify it in unit tests
    }

    @Test
    void testImportExcel_Success() {
        // Given
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("test.xlsx");
        when(mockFile.isEmpty()).thenReturn(false);

        // Mock the Excel parsing and processing
        // Note: This is a complex method that involves file processing,
        // so we're testing the basic flow rather than the detailed implementation

        // When
        Result<?> result = sporadicProjectService.importExcel(mockFile);

        // Then
        assertNotNull(result);
        // The actual result depends on the file content and processing logic
    }

    @Test
    void testImportExcel_EmptyFile() {
        // Given
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.isEmpty()).thenReturn(true);

        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.importExcel(mockFile));
    }

    @Test
    void testImportExcel_NullFile() {
        // When & Then
        assertThrows(ScException.class, () -> sporadicProjectService.importExcel(null));
    }

    // ==================== 测试数据准备和工具方法 ====================

    /**
     * 创建测试用的SporadicProject实体
     */
    private SporadicProject createTestSporadicProject(String id, String name) {
        return SporadicProject.builder()
                .id(id)
                .name(name)
                .organizationId("test-org-id")
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L))
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .status(0)
                .monitorFlag(0)
                .poiId("test-poi-id")
                .lat(new BigDecimal("39.9042"))
                .lng(new BigDecimal("116.4074"))
                .createUser("test-user-id")
                .isDeleted(0)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
    }

    /**
     * 创建测试用的SporadicProjectCreateDTO
     */
    private SporadicProjectCreateDTO createTestCreateDTO(String name) {
        return SporadicProjectCreateDTO.builder()
                .name(name)
                .catePid("test-cate-pid")
                .cateId("test-cate-id")
                .amount(new BigDecimal("100000"))
                .area(new BigDecimal("500"))
                .startAt(new Date())
                .endAt(new Date(System.currentTimeMillis() + 86400000L))
                .regionPid("test-region-pid")
                .regionId("test-region-id")
                .regionCid("test-region-cid")
                .address("测试地址")
                .constructorName("测试建设单位")
                .constructorCharger("测试负责人")
                .ownerMobile("13800138000")
                .contractorName("测试施工单位")
                .contractorCharger("测试施工负责人")
                .contractorChargerMobile("13800138001")
                .projectNumber("TEST-2025-001")
                .poiId("test-poi-id")
                .build();
    }

    /**
     * 创建测试用的SporadicProjectUpdateDTO
     */
    private SporadicProjectUpdateDTO createTestUpdateDTO(String id, String name) {
        return SporadicProjectUpdateDTO.builder()
                .id(id)
                .name(name)
                .amount(new BigDecimal("150000"))
                .area(new BigDecimal("600"))
                .poiId("test-poi-id")
                .build();
    }

    /**
     * 创建测试用的SporadicProjectRespDTO
     */
    private SporadicProjectRespDTO createTestRespDTO(String id, String name) {
        SporadicProjectRespDTO dto = new SporadicProjectRespDTO();
        dto.setId(id);
        dto.setName(name);
        dto.setAmount(new BigDecimal("100000"));
        dto.setArea(new BigDecimal("500"));
        dto.setStatus(0);
        return dto;
    }

    /**
     * 创建测试用的GisPoi
     */
    private GisPoi createTestGisPoi(String id) {
        GisPoi poi = new GisPoi();
        poi.setId(id);
        poi.setLat("39.9042");
        poi.setLng("116.4074");
        return poi;
    }

    /**
     * 设置通用的Mock行为
     */
    private void setupCommonMocks() {
        // 设置常用的Mock行为
        when(gisPoiRepository.findById(anyString())).thenReturn(Optional.of(testGisPoi));
        when(sporadicProjectRepository.save(any(SporadicProject.class))).thenReturn(testProject);
        when(sporadicProjectRepository.findById(anyString())).thenReturn(Optional.of(testProject));
    }

    /**
     * 验证常用的方法调用
     */
    private void verifyCommonCalls() {
        verify(sporadicProjectRepository).save(any(SporadicProject.class));
    }

    // ==================== 边界条件和异常场景测试 ====================

    @Test
    void testCreate_WithNullDTO() {
        // When & Then
        assertThrows(NullPointerException.class, () -> sporadicProjectService.create(null));
    }

    @Test
    void testUpdate_WithNullDTO() {
        // When & Then
        assertThrows(NullPointerException.class, () -> sporadicProjectService.update(null));
    }

    @Test
    void testDelete_WithNullIds() {
        // When
        sporadicProjectService.delete(null);

        // Then
        verify(sporadicProjectRepository, never()).findById(anyString());
        verify(sporadicProjectRepository, never()).save(any(SporadicProject.class));
    }

    @Test
    void testShow_WithNullDTO() {
        // When & Then
        assertThrows(NullPointerException.class, () -> sporadicProjectService.show(null));
    }
}
