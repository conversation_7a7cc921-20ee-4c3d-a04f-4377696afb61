package cn.shencom.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 测试配置类
 * 用于管理测试环境的全局参数和配置
 *
 * 支持通过环境变量设置：
 * - TEST_ORGANIZATION_ID: 组织机构ID
 * - TEST_SCID: 系统/服务标识
 * - Authorization: 认证令牌
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Configuration
@Data
public class TestConfiguration {

    /**
     * 全局参数配置
     */
    private GlobalParams globalParams = new GlobalParams();

    @lombok.Data
    public static class GlobalParams {
        /**
         * 组织机构ID - 从环境变量 TEST_ORGANIZATION_ID 读取
         */
        @Value("${TEST_ORGANIZATION_ID:test-org-id}")
        private String organizationId;

        /**
         * 系统/服务标识 - 从环境变量 TEST_SCID 读取
         */
        @Value("${TEST_SCID:test-scid}")
        private String scid;

        /**
         * 认证令牌 - 从环境变量 Authorization 读取
         */
        @Value("${Authorization:Bearer Authorization}")
        private String authToken;
    }

}
