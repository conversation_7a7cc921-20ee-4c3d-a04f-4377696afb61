package cn.shencom.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 测试配置类
 * 用于管理测试环境的全局参数和配置
 *
 * 支持通过环境变量设置：
 * - TEST_ORGANIZATION_ID: 组织机构ID (可选)
 * - TEST_SCID: 系统/服务标识 (必填)
 * - Authorization: 认证令牌 (必填)
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Configuration
@Data
public class TestConfiguration {

    /**
     * 全局参数配置
     */
    private GlobalParams globalParams = new GlobalParams();

    /**
     * 验证必填环境变量
     * TEST_SCID和Authorization必填，TEST_ORGANIZATION_ID可选
     */
    public static void validateRequiredEnvironmentVariables() {
        System.out.println("=== 环境变量验证 ===");

        // 检查 TEST_SCID (必填)
        String testScid = getEnvironmentOrSystemProperty("TEST_SCID");
        if (testScid == null || testScid.trim().isEmpty()) {
            throw new IllegalStateException("必填环境变量 TEST_SCID 未设置或为空。请设置环境变量或系统属性 -DTEST_SCID=your-scid");
        }
        System.out.println("✓ TEST_SCID: " + testScid);

        // 检查 Authorization (必填)
        String authorization = getEnvironmentOrSystemProperty("Authorization");
        if (authorization == null || authorization.trim().isEmpty()) {
            throw new IllegalStateException("必填环境变量 Authorization 未设置或为空。请设置环境变量或系统属性 -DAuthorization=\"Bearer your-token\"");
        }
        System.out.println("✓ Authorization: " + (authorization.length() > 50 ? authorization.substring(0, 50) + "..." : authorization));

        // 检查 TEST_ORGANIZATION_ID (可选)
        String testOrgId = getEnvironmentOrSystemProperty("TEST_ORGANIZATION_ID");
        if (testOrgId != null && !testOrgId.trim().isEmpty()) {
            System.out.println("✓ TEST_ORGANIZATION_ID: " + testOrgId);
        } else {
            System.out.println("ℹ TEST_ORGANIZATION_ID: 未设置，将使用默认值");
        }

        System.out.println("环境变量验证通过！");
        System.out.println("==================");
    }

    /**
     * 获取环境变量或系统属性值（系统属性优先）
     */
    public static String getEnvironmentOrSystemProperty(String key) {
        // 优先从系统属性读取
        String value = System.getProperty(key);
        if (value != null && !value.trim().isEmpty()) {
            return value;
        }
        // 然后从环境变量读取
        return System.getenv(key);
    }

    @org.springframework.stereotype.Component
    public static class GlobalParams {

        @Value("${TEST_ORGANIZATION_ID:test-org-id}")
        private String organizationId;

        @Value("${TEST_SCID:test-scid}")
        private String scid;

        @Value("${Authorization:Bearer Authorization}")
        private String authToken;

        /**
         * 组织机构ID - 从系统属性或环境变量 TEST_ORGANIZATION_ID 读取
         */
        public String getOrganizationId() {
            // 优先从系统属性读取，然后从环境变量读取，最后使用默认值
            String propValue = System.getProperty("TEST_ORGANIZATION_ID");
            if (propValue != null && !propValue.isEmpty()) {
                return propValue;
            }
            String envValue = System.getenv("TEST_ORGANIZATION_ID");
            if (envValue != null && !envValue.isEmpty()) {
                return envValue;
            }
            return organizationId;
        }

        /**
         * 系统/服务标识 - 从系统属性或环境变量 TEST_SCID 读取
         */
        public String getScid() {
            // 优先从系统属性读取，然后从环境变量读取，最后使用默认值
            String propValue = System.getProperty("TEST_SCID");
            if (propValue != null && !propValue.isEmpty()) {
                return propValue;
            }
            String envValue = System.getenv("TEST_SCID");
            if (envValue != null && !envValue.isEmpty()) {
                return envValue;
            }
            return scid;
        }

        /**
         * 认证令牌 - 从系统属性或环境变量 Authorization 读取
         */
        public String getAuthToken() {
            // 优先从系统属性读取，然后从环境变量读取，最后使用默认值
            String propValue = System.getProperty("Authorization");
            if (propValue != null && !propValue.isEmpty()) {
                return propValue;
            }
            String envValue = System.getenv("Authorization");
            if (envValue != null && !envValue.isEmpty()) {
                return envValue;
            }
            return authToken;
        }
    }

}
