package cn.shencom.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 测试配置类
 * 用于管理测试环境的全局参数和配置
 *
 * 支持通过环境变量设置：
 * - TEST_ORGANIZATION_ID: 组织机构ID
 * - TEST_SCID: 系统/服务标识
 * - Authorization: 认证令牌
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Configuration
@Data
public class TestConfiguration {

    /**
     * 全局参数配置
     */
    private GlobalParams globalParams = new GlobalParams();

    @org.springframework.stereotype.Component
    public static class GlobalParams {

        @Value("${TEST_ORGANIZATION_ID:test-org-id}")
        private String organizationId;

        @Value("${TEST_SCID:test-scid}")
        private String scid;

        @Value("${Authorization:Bearer Authorization}")
        private String authToken;

        /**
         * 组织机构ID - 从系统属性或环境变量 TEST_ORGANIZATION_ID 读取
         */
        public String getOrganizationId() {
            // 优先从系统属性读取，然后从环境变量读取，最后使用默认值
            String propValue = System.getProperty("TEST_ORGANIZATION_ID");
            if (propValue != null && !propValue.isEmpty()) {
                return propValue;
            }
            String envValue = System.getenv("TEST_ORGANIZATION_ID");
            if (envValue != null && !envValue.isEmpty()) {
                return envValue;
            }
            return organizationId != null ? organizationId : "test-org-id";
        }

        /**
         * 系统/服务标识 - 从系统属性或环境变量 TEST_SCID 读取
         */
        public String getScid() {
            // 优先从系统属性读取，然后从环境变量读取，最后使用默认值
            String propValue = System.getProperty("TEST_SCID");
            if (propValue != null && !propValue.isEmpty()) {
                return propValue;
            }
            String envValue = System.getenv("TEST_SCID");
            if (envValue != null && !envValue.isEmpty()) {
                return envValue;
            }
            return scid != null ? scid : "test-scid";
        }

        /**
         * 认证令牌 - 从系统属性或环境变量 Authorization 读取
         */
        public String getAuthToken() {
            // 优先从系统属性读取，然后从环境变量读取，最后使用默认值
            String propValue = System.getProperty("Authorization");
            if (propValue != null && !propValue.isEmpty()) {
                return propValue;
            }
            String envValue = System.getenv("Authorization");
            if (envValue != null && !envValue.isEmpty()) {
                return envValue;
            }
            return authToken != null ? authToken : "Bearer Authorization";
        }
    }

}
