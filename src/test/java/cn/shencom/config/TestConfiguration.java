package cn.shencom.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.TestPropertySource;

import java.math.BigDecimal;

/**
 * 测试配置类
 * 用于管理测试环境的全局参数和配置
 * 
 * 支持通过环境变量覆盖配置文件中的默认值：
 * - TEST_ORGANIZATION_ID: 组织机构ID
 * - TEST_SCID: 系统/服务标识
 * - TEST_AUTH_TOKEN: 认证令牌
 * - TEST_USER_ID: 用户ID
 * - TEST_USERNAME: 用户名
 * - TEST_USER_REALNAME: 用户真实姓名
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Configuration
@ConfigurationProperties(prefix = "test")
@Data
public class TestConfiguration {

    /**
     * 全局参数配置
     */
    private GlobalParams globalParams = new GlobalParams();
    
    /**
     * 测试数据配置
     */
    private Data data = new Data();
    
    /**
     * 分页配置
     */
    private Pagination pagination = new Pagination();
    
    /**
     * 功能开关配置
     */
    private Features features = new Features();

    @lombok.Data
    public static class GlobalParams {
        /**
         * 组织机构ID
         */
        private String organizationId = "test-org-id";
        
        /**
         * 系统/服务标识
         */
        private String scid = "test-scid";
        
        /**
         * 认证令牌
         */
        private String authToken = "Bearer test-token";
        
        /**
         * 用户ID
         */
        private String userId = "test-user-id";
        
        /**
         * 用户名
         */
        private String username = "testuser";
        
        /**
         * 用户真实姓名
         */
        private String userRealname = "测试用户";
    }

    @lombok.Data
    public static class Data {
        private ProjectData project = new ProjectData();
        private CategoryData category = new CategoryData();
        private RegionData region = new RegionData();
        private ConstructorData constructor = new ConstructorData();
        private ContractorData contractor = new ContractorData();
        private GisPoiData gisPoi = new GisPoiData();
    }

    @lombok.Data
    public static class ProjectData {
        private String id = "test-project-id";
        private String name = "测试工程";
        private BigDecimal amount = new BigDecimal("100000");
        private BigDecimal area = new BigDecimal("500");
        private String address = "测试地址";
        private String projectNumber = "TEST-2025-001";
    }

    @lombok.Data
    public static class CategoryData {
        private String pid = "test-cate-pid";
        private String id = "test-cate-id";
    }

    @lombok.Data
    public static class RegionData {
        private String pid = "test-region-pid";
        private String id = "test-region-id";
        private String cid = "test-region-cid";
    }

    @lombok.Data
    public static class ConstructorData {
        private String name = "测试建设单位";
        private String charger = "测试负责人";
        private String mobile = "13800138000";
    }

    @lombok.Data
    public static class ContractorData {
        private String name = "测试施工单位";
        private String charger = "测试施工负责人";
        private String mobile = "13800138001";
    }

    @lombok.Data
    public static class GisPoiData {
        private String id = "test-poi-id";
        private String lat = "39.9042";
        private String lng = "116.4074";
    }

    @lombok.Data
    public static class Pagination {
        private Integer defaultPage = 0;
        private Integer defaultSize = 10;
    }

    @lombok.Data
    public static class Features {
        /**
         * 是否启用权限验证测试
         */
        private Boolean enablePermissionTests = true;
        
        /**
         * 是否启用全局参数验证测试
         */
        private Boolean enableGlobalParamTests = true;
        
        /**
         * 是否启用异常场景测试
         */
        private Boolean enableExceptionTests = true;
    }
}
