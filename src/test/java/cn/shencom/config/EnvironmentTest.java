package cn.shencom.config;

import org.junit.jupiter.api.Test;

/**
 * 环境变量测试类
 * 用于验证环境变量是否能正确读取
 */
public class EnvironmentTest {

    @Test
    void testEnvironmentVariables() {
        System.out.println("=== 环境变量测试 ===");
        System.out.println("TEST_ORGANIZATION_ID: " + System.getenv("TEST_ORGANIZATION_ID"));
        System.out.println("TEST_SCID: " + System.getenv("TEST_SCID"));
        System.out.println("Authorization: " + System.getenv("Authorization"));
        System.out.println("==================");
        
        // 测试 System.getProperty
        System.out.println("=== 系统属性测试 ===");
        System.out.println("TEST_ORGANIZATION_ID: " + System.getProperty("TEST_ORGANIZATION_ID"));
        System.out.println("TEST_SCID: " + System.getProperty("TEST_SCID"));
        System.out.println("Authorization: " + System.getProperty("Authorization"));
        System.out.println("==================");
    }
}
