package cn.shencom.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 配置验证测试类
 * 用于验证测试环境的配置是否正确加载
 * 
 * 支持通过环境变量或系统属性设置：
 * - TEST_ORGANIZATION_ID: 组织机构ID
 * - TEST_SCID: 系统/服务标识
 * - Authorization: 认证令牌
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = TestConfiguration.class)
class ConfigurationValidationTest {

    @Autowired
    private TestConfiguration testConfig;

    @Test
    void testEnvironmentVariablesAndSystemProperties() {
        // 输出环境变量信息用于调试
        System.out.println("=== 环境变量信息 ===");
        System.out.println("TEST_ORGANIZATION_ID: " + System.getenv("TEST_ORGANIZATION_ID"));
        System.out.println("TEST_SCID: " + System.getenv("TEST_SCID"));
        System.out.println("Authorization: " + (System.getenv("Authorization") != null ? 
            System.getenv("Authorization").substring(0, Math.min(50, System.getenv("Authorization").length())) + "..." : "null"));
        System.out.println("==================");
        
        // 输出系统属性信息用于调试
        System.out.println("=== 系统属性信息 ===");
        System.out.println("TEST_ORGANIZATION_ID: " + System.getProperty("TEST_ORGANIZATION_ID"));
        System.out.println("TEST_SCID: " + System.getProperty("TEST_SCID"));
        System.out.println("Authorization: " + (System.getProperty("Authorization") != null ? 
            System.getProperty("Authorization").substring(0, Math.min(50, System.getProperty("Authorization").length())) + "..." : "null"));
        System.out.println("==================");
    }

    @Test
    void testConfigurationLoading() {
        // 验证配置是否正确加载
        assertNotNull(testConfig, "测试配置对象不能为空");
        assertNotNull(testConfig.getGlobalParams(), "全局参数配置不能为空");
        
        // 验证全局参数配置
        String orgId = testConfig.getGlobalParams().getOrganizationId();
        String scid = testConfig.getGlobalParams().getScid();
        String authToken = testConfig.getGlobalParams().getAuthToken();
        
        assertNotNull(orgId, "组织ID不能为空");
        assertNotNull(scid, "服务标识不能为空");
        assertNotNull(authToken, "认证令牌不能为空");
        
        // 输出配置信息用于调试
        System.out.println("=== 测试配置信息 ===");
        System.out.println("组织ID: " + orgId);
        System.out.println("服务标识: " + scid);
        System.out.println("认证令牌: " + (authToken.length() > 50 ? authToken.substring(0, 50) + "..." : authToken));
        System.out.println("==================");
        
        // 验证配置值不是默认值（如果设置了环境变量或系统属性）
        String envOrgId = System.getenv("TEST_ORGANIZATION_ID");
        String propOrgId = System.getProperty("TEST_ORGANIZATION_ID");
        
        if ((envOrgId != null && !envOrgId.isEmpty()) || (propOrgId != null && !propOrgId.isEmpty())) {
            assertNotEquals("test-org-id", orgId, "组织ID应该使用环境变量或系统属性的值，而不是默认值");
            System.out.println("✓ 组织ID正确使用了自定义值: " + orgId);
        } else {
            assertEquals("test-org-id", orgId, "未设置环境变量时应该使用默认值");
            System.out.println("ℹ 未设置环境变量 TEST_ORGANIZATION_ID，使用默认值: " + orgId);
        }
        
        String envScid = System.getenv("TEST_SCID");
        String propScid = System.getProperty("TEST_SCID");
        
        if ((envScid != null && !envScid.isEmpty()) || (propScid != null && !propScid.isEmpty())) {
            assertNotEquals("test-scid", scid, "SCID应该使用环境变量或系统属性的值，而不是默认值");
            System.out.println("✓ SCID正确使用了自定义值: " + scid);
        } else {
            assertEquals("test-scid", scid, "未设置环境变量时应该使用默认值");
            System.out.println("ℹ 未设置环境变量 TEST_SCID，使用默认值: " + scid);
        }
        
        String envAuth = System.getenv("Authorization");
        String propAuth = System.getProperty("Authorization");
        
        if ((envAuth != null && !envAuth.isEmpty()) || (propAuth != null && !propAuth.isEmpty())) {
            assertNotEquals("Bearer Authorization", authToken, "认证令牌应该使用环境变量或系统属性的值，而不是默认值");
            System.out.println("✓ 认证令牌正确使用了自定义值");
        } else {
            assertEquals("Bearer Authorization", authToken, "未设置环境变量时应该使用默认值");
            System.out.println("ℹ 未设置环境变量 Authorization，使用默认值: " + authToken);
        }
    }

    @Test
    void testConfigurationPriority() {
        // 测试配置优先级：系统属性 > 环境变量 > 默认值
        System.out.println("=== 配置优先级测试 ===");
        
        String orgId = testConfig.getGlobalParams().getOrganizationId();
        String scid = testConfig.getGlobalParams().getScid();
        String authToken = testConfig.getGlobalParams().getAuthToken();
        
        // 检查系统属性是否优先于环境变量
        String propOrgId = System.getProperty("TEST_ORGANIZATION_ID");
        String envOrgId = System.getenv("TEST_ORGANIZATION_ID");
        
        if (propOrgId != null && !propOrgId.isEmpty()) {
            assertEquals(propOrgId, orgId, "系统属性应该优先于环境变量");
            System.out.println("✓ 组织ID优先使用系统属性: " + propOrgId);
        } else if (envOrgId != null && !envOrgId.isEmpty()) {
            assertEquals(envOrgId, orgId, "环境变量应该优先于默认值");
            System.out.println("✓ 组织ID使用环境变量: " + envOrgId);
        } else {
            assertEquals("test-org-id", orgId, "应该使用默认值");
            System.out.println("ℹ 组织ID使用默认值: " + orgId);
        }
        
        System.out.println("配置优先级验证通过！");
        System.out.println("==================");
    }

    @Test
    void testConfigurationValidation() {
        // 验证配置的有效性
        System.out.println("=== 配置有效性验证 ===");
        
        String orgId = testConfig.getGlobalParams().getOrganizationId();
        String scid = testConfig.getGlobalParams().getScid();
        String authToken = testConfig.getGlobalParams().getAuthToken();
        
        // 验证组织ID格式（如果不是默认值）
        if (!"test-org-id".equals(orgId)) {
            assertTrue(orgId.length() > 0, "组织ID不能为空字符串");
            System.out.println("✓ 组织ID格式有效: " + orgId);
        }
        
        // 验证SCID格式（如果不是默认值）
        if (!"test-scid".equals(scid)) {
            assertTrue(scid.length() > 0, "SCID不能为空字符串");
            System.out.println("✓ SCID格式有效: " + scid);
        }
        
        // 验证认证令牌格式（如果不是默认值）
        if (!"Bearer Authorization".equals(authToken)) {
            assertTrue(authToken.length() > 0, "认证令牌不能为空字符串");
            System.out.println("✓ 认证令牌格式有效");
        }
        
        System.out.println("配置有效性验证通过！");
        System.out.println("==================");
    }
}
