# 测试配置说明

本文档说明如何通过环境变量自定义 `SporadicProjectServiceImplTest` 的测试参数。

## 测试范围

本测试类专注于测试 `SporadicProjectServiceImpl.getUserProjectList()` 方法，包括：
- 成功获取用户项目列表
- 处理空结果情况
- 处理无用户项目情况
- 参数验证和异常处理

## 配置方式

测试环境使用 `src/main/resources/bootstrap.yml` 进行配置，通过环境变量覆盖默认值。

## 环境变量支持

以下环境变量可以设置测试参数：

### 核心全局参数

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `TEST_ORGANIZATION_ID` | `test-org-id` | 组织机构ID |
| `TEST_SCID` | `test-scid` | 系统/服务标识 |
| `Authorization` | `Bearer Authorization` | 认证令牌 |



## 使用示例

### 1. 使用带配置验证的测试脚本（推荐）

```bash
# 创建本地环境变量文件
cp .env.test.local.example .env.test.local

# 编辑配置文件
vim .env.test.local

# 运行带配置验证的测试（推荐）
./scripts/run-tests-with-validation.sh
```

### 2. 使用普通测试脚本

```bash
# 运行普通测试（不包含配置验证）
./scripts/run-tests-with-env.sh
```

### 3. 手动创建环境变量文件

```bash
# 创建 .env.test.local 文件
echo 'TEST_ORGANIZATION_ID=my-org-123' > .env.test.local
echo 'TEST_SCID=my-scid' >> .env.test.local
echo 'Authorization=Bearer my-token' >> .env.test.local

# 运行带配置验证的测试脚本
./scripts/run-tests-with-validation.sh
```

### 4. 直接通过系统属性运行测试

```bash
# 运行配置验证测试
mvn test -Dtest=ConfigurationValidationTest \
  -DTEST_ORGANIZATION_ID=my-org-123 \
  -DTEST_SCID=my-service-scid \
  -DAuthorization="Bearer my-custom-token"

# 运行主要测试
mvn test -Dtest=SporadicProjectServiceImplTest \
  -DTEST_ORGANIZATION_ID=my-org-123 \
  -DTEST_SCID=my-service-scid \
  -DAuthorization="Bearer my-custom-token"
```

### 5. 在IDE中设置环境变量

在IntelliJ IDEA中：
1. 打开 Run/Debug Configurations
2. 选择测试配置
3. 在 VM options 中添加系统属性：`-DTEST_ORGANIZATION_ID=your-value`

## 配置优先级

配置的优先级从高到低为：
1. 系统属性 (-D参数) - **最高优先级**
2. 环境变量（从 .env.test.local 文件加载）
3. bootstrap.yml 配置文件
4. 代码中的默认值

**注意**: 系统属性优先于环境变量，这样可以在运行时灵活覆盖配置。

## 测试方法说明

### 配置验证测试 (ConfigurationValidationTest)

| 测试方法 | 测试场景 | 说明 |
|---------|---------|------|
| `testEnvironmentVariablesAndSystemProperties` | 环境变量和系统属性检查 | 输出当前环境变量和系统属性值 |
| `testConfigurationLoading` | 配置加载验证 | 验证测试配置是否正确加载 |
| `testConfigurationPriority` | 配置优先级验证 | 验证系统属性优先于环境变量 |
| `testConfigurationValidation` | 配置有效性验证 | 验证配置值的格式和有效性 |

### getUserProjectList 相关测试 (SporadicProjectServiceImplTest)

| 测试方法 | 测试场景 | 说明 |
|---------|---------|------|
| `testGetUserProjectList_WithRegionAndDto_Success` | 正常获取项目列表 | 验证带区域参数的成功调用 |
| `testGetUserProjectList_WithRegionAndDto_NoUserProject` | 无用户项目 | 验证用户无项目时返回空列表 |
| `testGetUserProjectList_NoParams_Success` | 无参数成功调用 | 验证无参数版本的成功调用 |
| `testGetUserProjectList_EmptyResult` | 空结果处理 | 验证查询结果为空的情况 |
| `testGetUserProjectList_NoUserProject` | 无用户项目处理 | 验证用户无项目权限的情况 |

## 测试流程

### 推荐的测试流程

1. **配置验证阶段**: 首先运行 `ConfigurationValidationTest` 验证配置是否正确
2. **主要测试阶段**: 配置验证通过后，运行 `SporadicProjectServiceImplTest` 进行功能测试

使用 `./scripts/run-tests-with-validation.sh` 脚本可以自动执行这个两阶段流程。

### 测试输出示例

```
==========================================
第一步: 运行配置验证测试
==========================================
✅ 配置验证测试通过！

==========================================
第二步: 运行主要测试
==========================================
✅ 所有测试通过！

==========================================
测试总结
==========================================
✓ 配置验证测试: 通过
✓ 主要功能测试: 通过
✓ 环境变量配置: 正确
==========================================
```

## 注意事项

1. **配置优先级**: 系统属性 > 环境变量 > 默认值
2. **配置验证**: 建议先运行配置验证测试，确保配置正确
3. **环境变量**: 名称区分大小写，支持从 `.env.test.local` 文件加载
4. **测试范围**: 专注于 `getUserProjectList` 方法，简化维护复杂度
5. **默认值**: 所有配置都有合理的默认值，无配置也能运行

## 故障排除

如果测试运行异常，请检查：

### 配置相关问题
1. 运行配置验证测试：`mvn test -Dtest=ConfigurationValidationTest`
2. 检查环境变量文件：`.env.test.local` 是否存在且格式正确
3. 验证系统属性传递：查看测试输出中的系统属性信息

### 测试相关问题
1. 查看测试日志中的配置加载信息
2. 确认 `getUserProjectList` 方法的依赖项是否正确模拟
3. 检查 Spring Boot 上下文是否正常启动

### 常见错误
- **环境变量未生效**: 使用系统属性 `-D` 参数替代环境变量
- **配置验证失败**: 检查配置值格式，确保不为空
- **Spring 上下文启动失败**: 检查 `bootstrap.yml` 配置和依赖项
