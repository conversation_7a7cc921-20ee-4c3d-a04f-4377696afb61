# 测试配置说明

本文档说明如何通过环境变量或配置文件自定义 `SporadicProjectServiceImplTest` 的测试参数。

## 配置文件

测试配置文件位于：`src/test/resources/application-test.yml`

## 环境变量支持

以下环境变量可以覆盖配置文件中的默认值：

### 核心全局参数

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_ORGANIZATION_ID` | `test.global-params.organization-id` | `test-org-id` | 组织机构ID |
| `TEST_SCID` | `test.global-params.scid` | `test-scid` | 系统/服务标识 |
| `Authorization` | `test.global-params.Authorization` | `Bearer Authorization` | 认证令牌 |



## 使用示例

### 1. 使用测试脚本（推荐）

```bash
# 创建本地环境变量文件
cp .env.test.local.example .env.test.local

# 编辑配置文件
vim .env.test.local

# 运行测试
./scripts/run-tests-with-env.sh
```

### 2. 手动创建环境变量文件

```bash
# 创建 .env.test.local 文件
echo 'TEST_ORGANIZATION_ID=my-org-123' > .env.test.local
echo 'TEST_SCID=my-scid' >> .env.test.local
echo 'Authorization=Bearer my-token' >> .env.test.local

# 运行测试脚本
./scripts/run-tests-with-env.sh
```

### 3. 直接通过环境变量运行测试

```bash
# 设置环境变量
export TEST_ORGANIZATION_ID=my-org-123
export TEST_SCID=my-service-scid
export Authorization="Bearer my-custom-token"

# 运行测试
mvn test -Dtest=SporadicProjectServiceImplTest
```

### 4. 在IDE中设置环境变量

在IntelliJ IDEA中：
1. 打开 Run/Debug Configurations
2. 选择测试配置
3. 在 Environment variables 中添加所需的环境变量

## 配置优先级

配置的优先级从高到低为：
1. 环境变量
2. 系统属性 (-D参数)
3. application-test.yml 配置文件
4. 代码中的默认值

## 注意事项

1. 所有配置都有合理的默认值，即使不设置任何环境变量，测试也能正常运行
2. 环境变量名称区分大小写
3. 布尔类型的环境变量接受 `true`/`false` 值
4. 数值类型的环境变量会自动转换类型
5. 如果环境变量值格式不正确，将使用默认值并在日志中输出警告

## 故障排除

如果测试运行异常，请检查：
1. 环境变量名称是否正确
2. 环境变量值的格式是否正确
3. 配置文件语法是否正确
4. 查看测试日志中的配置加载信息
