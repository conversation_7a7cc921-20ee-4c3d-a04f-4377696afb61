# 测试配置说明

本文档说明如何通过环境变量或配置文件自定义 `SporadicProjectServiceImplTest` 的测试参数。

## 配置文件

测试配置文件位于：`src/test/resources/application-test.yml`

## 环境变量支持

以下环境变量可以覆盖配置文件中的默认值：

### 全局参数

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_ORGANIZATION_ID` | `test.global-params.organization-id` | `test-org-id` | 组织机构ID |
| `TEST_SCID` | `test.global-params.scid` | `test-scid` | 系统/服务标识 |
| `TEST_AUTH_TOKEN` | `test.global-params.auth-token` | `Bearer test-token` | 认证令牌 |
| `TEST_USER_ID` | `test.global-params.user-id` | `test-user-id` | 用户ID |
| `TEST_USERNAME` | `test.global-params.username` | `testuser` | 用户名 |
| `TEST_USER_REALNAME` | `test.global-params.user-realname` | `测试用户` | 用户真实姓名 |

### 项目数据

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_PROJECT_ID` | `test.data.project.id` | `test-project-id` | 项目ID |
| `TEST_PROJECT_NAME` | `test.data.project.name` | `测试工程` | 项目名称 |
| `TEST_PROJECT_AMOUNT` | `test.data.project.amount` | `100000` | 项目金额 |
| `TEST_PROJECT_AREA` | `test.data.project.area` | `500` | 项目面积 |
| `TEST_PROJECT_ADDRESS` | `test.data.project.address` | `测试地址` | 项目地址 |
| `TEST_PROJECT_NUMBER` | `test.data.project.project-number` | `TEST-2025-001` | 项目编号 |

### 分类数据

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_CATE_PID` | `test.data.category.pid` | `test-cate-pid` | 分类父ID |
| `TEST_CATE_ID` | `test.data.category.id` | `test-cate-id` | 分类ID |

### 区域数据

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_REGION_PID` | `test.data.region.pid` | `test-region-pid` | 区域父ID |
| `TEST_REGION_ID` | `test.data.region.id` | `test-region-id` | 区域ID |
| `TEST_REGION_CID` | `test.data.region.cid` | `test-region-cid` | 区域子ID |

### 建设单位数据

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_CONSTRUCTOR_NAME` | `test.data.constructor.name` | `测试建设单位` | 建设单位名称 |
| `TEST_CONSTRUCTOR_CHARGER` | `test.data.constructor.charger` | `测试负责人` | 建设单位负责人 |
| `TEST_OWNER_MOBILE` | `test.data.constructor.mobile` | `13800138000` | 建设单位联系电话 |

### 施工单位数据

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_CONTRACTOR_NAME` | `test.data.contractor.name` | `测试施工单位` | 施工单位名称 |
| `TEST_CONTRACTOR_CHARGER` | `test.data.contractor.charger` | `测试施工负责人` | 施工单位负责人 |
| `TEST_CONTRACTOR_MOBILE` | `test.data.contractor.mobile` | `13800138001` | 施工单位联系电话 |

### GIS点位数据

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_POI_ID` | `test.data.gis-poi.id` | `test-poi-id` | GIS点位ID |
| `TEST_POI_LAT` | `test.data.gis-poi.lat` | `39.9042` | 纬度 |
| `TEST_POI_LNG` | `test.data.gis-poi.lng` | `116.4074` | 经度 |

### 分页配置

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_DEFAULT_PAGE` | `test.pagination.default-page` | `0` | 默认页码 |
| `TEST_DEFAULT_SIZE` | `test.pagination.default-size` | `10` | 默认页大小 |

### 功能开关

| 环境变量 | 配置路径 | 默认值 | 说明 |
|---------|---------|--------|------|
| `TEST_ENABLE_PERMISSION` | `test.features.enable-permission-tests` | `true` | 是否启用权限验证测试 |
| `TEST_ENABLE_GLOBAL_PARAMS` | `test.features.enable-global-param-tests` | `true` | 是否启用全局参数验证测试 |
| `TEST_ENABLE_EXCEPTIONS` | `test.features.enable-exception-tests` | `true` | 是否启用异常场景测试 |

## 使用示例

### 1. 通过环境变量运行测试

```bash
# 设置环境变量
export TEST_ORGANIZATION_ID=my-org-123
export TEST_SCID=my-service-scid
export TEST_AUTH_TOKEN="Bearer my-custom-token"
export TEST_PROJECT_NAME="我的测试项目"

# 运行测试
mvn test -Dtest=SporadicProjectServiceImplTest
```

### 2. 通过系统属性运行测试

```bash
mvn test -Dtest=SporadicProjectServiceImplTest \
  -DTEST_ORGANIZATION_ID=my-org-123 \
  -DTEST_SCID=my-service-scid \
  -DTEST_PROJECT_NAME="我的测试项目"
```

### 3. 在IDE中设置环境变量

在IntelliJ IDEA中：
1. 打开 Run/Debug Configurations
2. 选择测试配置
3. 在 Environment variables 中添加所需的环境变量

### 4. 修改配置文件

直接编辑 `src/test/resources/application-test.yml` 文件来修改默认值。

## 配置优先级

配置的优先级从高到低为：
1. 环境变量
2. 系统属性 (-D参数)
3. application-test.yml 配置文件
4. 代码中的默认值

## 注意事项

1. 所有配置都有合理的默认值，即使不设置任何环境变量，测试也能正常运行
2. 环境变量名称区分大小写
3. 布尔类型的环境变量接受 `true`/`false` 值
4. 数值类型的环境变量会自动转换类型
5. 如果环境变量值格式不正确，将使用默认值并在日志中输出警告

## 故障排除

如果测试运行异常，请检查：
1. 环境变量名称是否正确
2. 环境变量值的格式是否正确
3. 配置文件语法是否正确
4. 查看测试日志中的配置加载信息
