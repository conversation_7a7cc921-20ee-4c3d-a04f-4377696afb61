# 测试环境配置文件
# 用于 SporadicProjectServiceImplTest 的全局参数配置

test:
  # 全局参数配置
  global-params:
    # 组织机构ID - 可通过环境变量 TEST_ORGANIZATION_ID 覆盖
    organization-id: ${TEST_ORGANIZATION_ID:test-org-id}
    
    # 系统/服务标识 - 可通过环境变量 TEST_SCID 覆盖
    scid: ${TEST_SCID:test-scid}
    
    # 认证令牌 - 可通过环境变量 TEST_AUTH_TOKEN 覆盖
    auth-token: ${TEST_AUTH_TOKEN:Bearer test-token}
    
    # 用户ID - 可通过环境变量 TEST_USER_ID 覆盖
    user-id: ${TEST_USER_ID:test-user-id}
    
    # 用户名 - 可通过环境变量 TEST_USERNAME 覆盖
    username: ${TEST_USERNAME:testuser}
    
    # 用户真实姓名 - 可通过环境变量 TEST_USER_REALNAME 覆盖
    user-realname: ${TEST_USER_REALNAME:测试用户}

  # 测试数据配置
  data:
    # 项目相关测试数据
    project:
      id: ${TEST_PROJECT_ID:test-project-id}
      name: ${TEST_PROJECT_NAME:测试工程}
      amount: ${TEST_PROJECT_AMOUNT:100000}
      area: ${TEST_PROJECT_AREA:500}
      address: ${TEST_PROJECT_ADDRESS:测试地址}
      project-number: ${TEST_PROJECT_NUMBER:TEST-2025-001}
      
    # 分类相关测试数据
    category:
      pid: ${TEST_CATE_PID:test-cate-pid}
      id: ${TEST_CATE_ID:test-cate-id}
      
    # 区域相关测试数据
    region:
      pid: ${TEST_REGION_PID:test-region-pid}
      id: ${TEST_REGION_ID:test-region-id}
      cid: ${TEST_REGION_CID:test-region-cid}
      
    # 建设单位相关测试数据
    constructor:
      name: ${TEST_CONSTRUCTOR_NAME:测试建设单位}
      charger: ${TEST_CONSTRUCTOR_CHARGER:测试负责人}
      mobile: ${TEST_OWNER_MOBILE:13800138000}
      
    # 施工单位相关测试数据
    contractor:
      name: ${TEST_CONTRACTOR_NAME:测试施工单位}
      charger: ${TEST_CONTRACTOR_CHARGER:测试施工负责人}
      mobile: ${TEST_CONTRACTOR_MOBILE:13800138001}
      
    # GIS点位相关测试数据
    gis-poi:
      id: ${TEST_POI_ID:test-poi-id}
      lat: ${TEST_POI_LAT:39.9042}
      lng: ${TEST_POI_LNG:116.4074}

  # 分页配置
  pagination:
    default-page: ${TEST_DEFAULT_PAGE:0}
    default-size: ${TEST_DEFAULT_SIZE:10}
    
  # 测试开关配置
  features:
    # 是否启用权限验证测试
    enable-permission-tests: ${TEST_ENABLE_PERMISSION:true}
    
    # 是否启用全局参数验证测试
    enable-global-param-tests: ${TEST_ENABLE_GLOBAL_PARAMS:true}
    
    # 是否启用异常场景测试
    enable-exception-tests: ${TEST_ENABLE_EXCEPTIONS:true}

# Spring Boot 测试配置
spring:
  profiles:
    active: test
  
  # 数据源配置（测试环境）
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
  # 日志配置
  logging:
    level:
      cn.shencom: DEBUG
      org.springframework.test: INFO
