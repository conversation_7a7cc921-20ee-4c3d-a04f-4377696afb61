# 测试配置文件 - Properties 格式
# 这是 application-test.yml 的 Properties 格式版本

# 全局参数配置
test.global-params.organization-id=${TEST_ORGANIZATION_ID:test-org-id}
test.global-params.scid=${TEST_SCID:test-scid}
test.global-params.auth-token=${TEST_AUTH_TOKEN:Bearer test-token}
test.global-params.user-id=${TEST_USER_ID:test-user-id}
test.global-params.username=${TEST_USERNAME:testuser}
test.global-params.user-realname=${TEST_USER_REALNAME:测试用户}

# 项目数据配置
test.data.project.id=${TEST_PROJECT_ID:test-project-id}
test.data.project.name=${TEST_PROJECT_NAME:测试工程}
test.data.project.amount=${TEST_PROJECT_AMOUNT:100000}
test.data.project.area=${TEST_PROJECT_AREA:500}
test.data.project.address=${TEST_PROJECT_ADDRESS:测试地址}
test.data.project.project-number=${TEST_PROJECT_NUMBER:TEST-2025-001}

# 分类数据配置
test.data.category.pid=${TEST_CATE_PID:test-cate-pid}
test.data.category.id=${TEST_CATE_ID:test-cate-id}

# 区域数据配置
test.data.region.pid=${TEST_REGION_PID:test-region-pid}
test.data.region.id=${TEST_REGION_ID:test-region-id}
test.data.region.cid=${TEST_REGION_CID:test-region-cid}

# 建设单位数据配置
test.data.constructor.name=${TEST_CONSTRUCTOR_NAME:测试建设单位}
test.data.constructor.charger=${TEST_CONSTRUCTOR_CHARGER:测试负责人}
test.data.constructor.mobile=${TEST_OWNER_MOBILE:13800138000}

# 施工单位数据配置
test.data.contractor.name=${TEST_CONTRACTOR_NAME:测试施工单位}
test.data.contractor.charger=${TEST_CONTRACTOR_CHARGER:测试施工负责人}
test.data.contractor.mobile=${TEST_CONTRACTOR_MOBILE:13800138001}

# GIS点位数据配置
test.data.gis-poi.id=${TEST_POI_ID:test-poi-id}
test.data.gis-poi.lat=${TEST_POI_LAT:39.9042}
test.data.gis-poi.lng=${TEST_POI_LNG:116.4074}

# 分页配置
test.pagination.default-page=${TEST_DEFAULT_PAGE:0}
test.pagination.default-size=${TEST_DEFAULT_SIZE:10}

# 功能开关配置
test.features.enable-permission-tests=${TEST_ENABLE_PERMISSION:true}
test.features.enable-global-param-tests=${TEST_ENABLE_GLOBAL_PARAMS:true}
test.features.enable-exception-tests=${TEST_ENABLE_EXCEPTIONS:true}

# Spring Boot 测试配置
spring.profiles.active=test
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.logging.level.cn.shencom=DEBUG
spring.logging.level.org.springframework.test=INFO
