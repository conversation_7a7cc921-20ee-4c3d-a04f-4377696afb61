package cn.shencom.repos;

import cn.shencom.model.XsgcBusinessMembers;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Set;

/**
 * 小散工程-业务人员 的Repository
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Repository
public interface XsgcBusinessMembersRepository
        extends JpaRepository<XsgcBusinessMembers, String>, JpaSpecificationExecutor<XsgcBusinessMembers> {

    XsgcBusinessMembers findFirstByMobile(String mobile);

    XsgcBusinessMembers findFirstByUserId(String userId);

    XsgcBusinessMembers findFirstByUserIdAndStatus(String userId, Integer status);


    XsgcBusinessMembers findFirstByUserIdAndStatus(String userId,Integer status);

    @Query(nativeQuery = true, value = "SELECT DISTINCT user_id FROM xsgc_business_members WHERE type = ?1 AND is_deleted = 0 ")
    Set<String> findAllUserIdByType(Integer type);
}
